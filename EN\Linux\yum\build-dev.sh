#!/bin/bash
# $1 - wyn version
# $2 - dependency folder
# $3 - content folder
# $4 - yum repository url
# $5 - yum repository sas

WynVersion=9.0

RpmBuildDir=/var/rpmbuild
RpmFileName=wyn-enterprise-$1-main.x86_64.rpm

WynDir="wyn-$1"
SpecFile="$RpmBuildDir/SPECS/wynenterprise-$1.spec"

FontFile="$2/liberation-fonts-ttf-2.00.1.tar.gz"
MonetDBPath="$2/monetdb-11.43.23-yum.tar.gz"
JRE8File="$2/jre8.tar.gz"

# RepoURL="https://gcesartifacts.blob.core.windows.net/artifacts/rpm_files/$WynVersion-dev"
RepoURL=$4
RepoSAS=$5

# check parameter
if [[ "$1" != "$WynVersion.00"* ]]; then
  echo "invalid version number, the version number should be $WynVersion.00xxx.0"
  exit 1
fi

echo "prepare files"
rm -rf $WynDir > /dev/null 2>&1
mkdir -p $WynDir
cp -rf $3/* $WynDir
shopt -s globstar
rm -rf ./**/runtimes/browser*
rm -rf ./**/runtimes/macc*
rm -rf ./**/runtimes/android*
rm -rf ./**/runtimes/solaris*
rm -rf ./**/runtimes/tvos*
rm -rf ./**/runtimes/freebsd*
rm -rf ./**/runtimes/illumos*
rm -rf ./**/runtimes/ios*
rm -rf ./**/runtimes/osx*
rm -rf ./**/runtimes/alpine*
rm -rf ./**/runtimes/linux-mips*
rm -rf ./**/runtimes/linux-musl*
rm -rf ./**/runtimes/linux-s390*
rm -rf ./**/runtimes/linux-ppc64*
rm -rf ./**/runtimes/linux-arm*
rm -rf ./**/runtimes/win*
rm -rf $WynDir/ServiceRunner
cp ../configure.sh $WynDir/Monitor
mv $WynDir/Monitor/conf/Wyn.conf $WynDir/Monitor/conf/Wyn.conf.origin
cp -rf ../services $WynDir/
cp $JRE8File $WynDir/
cp $FontFile $WynDir/
cp $MonetDBPath $WynDir/monetdb-11.43.23.tar.gz

WynDir="$(pwd)/$WynDir"
echo "WynDir is $WynDir"

# generate SPEC file
echo "generate SPEC file"
echo "
%global __os_install_post %{nil}

Name: wyn-enterprise
Version: $1
Summary: Wyn Enterprise
Vendor: wynEnterprise
Release: main
License: GPL
AutoReqProv: no
Requires: aspnetcore-runtime-8.0 >= 8.0.16, curl, lz4

%description
Wyn Enterprise by wynEnterprise

%install
echo \"starting install...\"
if [ \"/\" != \"\$RPM_BUILD_ROOT\" ]; then
  rm -rf \$RPM_BUILD_ROOT/opt/Wyn
fi
mkdir -p \$RPM_BUILD_ROOT/opt/Wyn
cp -r $WynDir/* \$RPM_BUILD_ROOT/opt/Wyn
echo \"install finished...\"

%files
/opt/Wyn

%config(missingok) /opt/Wyn/*

%clean
rm -rf \$RPM_BUILD_ROOT

%pre
# fresh installation
if [ \$1 == 1 ]; then
  echo \"pre install checking...\"
# upgrade installation
elif [ \$1 -gt 1 ]; then
  echo \"upgrade checking...\"
  if [ -f /opt/Wyn/Server/version.txt ]; then
    preVersion=\$(cat /opt/Wyn/Server/version.txt)
    echo \"\$preVersion\" > \"/opt/Wyn/.upgrade\"
    echo \"checking upgrade from version \$preVersion ...\"
    if [[ \"\$preVersion\" != \"8.1.00\"* ]] && [[ \"\$preVersion\" != \"9.0.00\"* ]]; then
      echo -e \"\\e[31mcan not upgrade to Wyn 9.0 from version \$preVersion\\e[0m\"
      exit 1
    fi
  else
    echo \"9.0.00001.0\" > \"/opt/Wyn/.upgrade\"
  fi
  systemctl stop wyn.service > /dev/null 2>/dev/null
  systemctl stop wyn-datasource.service > /dev/null 2>/dev/null
fi
exit 0

%post
# fresh installation
if [ \$1 == 1 ]; then
  echo -e \"\\e[32mAfter the installation, you must run a script to configure the application using the command 'sudo bash /opt/Wyn/Monitor/configure.sh'.\\e[0m\"
# upgrade installation
elif [ \$1 -gt 1 ]; then
  INSTALL_SERVER=false
  INSTALL_REPORTING_WORKER=false
  preVersion=\$(cat \"/opt/Wyn/.upgrade\")
  if [ -f \"/opt/Wyn/.modules\" ]; then  # upgrade from 4.1
    Modules=\"\$(<\"/opt/Wyn/.modules\")\"
    # GEF-10212
    if [ \"\" == \"\$Modules\" ]; then
      Modules=\"Server,ReportingWorker,CotWorker,DashboardWorker\"
      echo \"\$Modules\" > \"/opt/Wyn/.modules\"
    fi
    UpperModules=\"\${Modules^^}\"
    if [[ \"\$UpperModules\" != *\"SERVER\"* ]]; then
      rm -rf \"/opt/Wyn/Server\" > /dev/null 2>/dev/null
      rm -rf \"/opt/Wyn/Plugins\" > /dev/null 2>/dev/null
      rm -rf \"/opt/Wyn/sampledata\" > /dev/null 2>/dev/null
      rm -rf \"/opt/Wyn/DataSourceService\" > /dev/null 2>/dev/null
      rm -rf \"/opt/Wyn/MemoryDBService\" > /dev/null 2>/dev/null
      rm -rf \"/opt/Wyn/SchedulerService\" > /dev/null 2>/dev/null
      rm -rf \"/opt/Wyn/jre\" > /dev/null 2>/dev/null
      INSTALL_SERVER=false
    else
      INSTALL_SERVER=true
    fi
    if [[ \"\$UpperModules\" != *\"REPORTINGWORKER\"* ]]; then
      rm -rf \"/opt/Wyn/ReportingWorker\" > /dev/null 2>/dev/null
      INSTALL_REPORTING_WORKER=false
    else
      INSTALL_REPORTING_WORKER=true
    fi
    if [[ \"\$UpperModules\" != *\"COTWORKER\"* ]]; then
      rm -rf \"/opt/Wyn/CotWorker\" > /dev/null 2>/dev/null
    fi
    if [[ \"\$UpperModules\" != *\"DASHBOARDWORKER\"* ]]; then
      rm -rf \"/opt/Wyn/DashboardWorker\" > /dev/null 2>/dev/null
    fi
    if [ -f \"/opt/Wyn/conf/Wyn.conf\" ]; then
      rm -rf \"/opt/Wyn/monetdb\" > /dev/null 2>/dev/null
      rm -rf \"/opt/Wyn/Monitor\" > /dev/null 2>/dev/null
      rm -rf \"/opt/Wyn/MonitorUpdater\" > /dev/null 2>/dev/null
    fi
  else # upgrade from 3.6
    echo \"Server,ReportingWorker,CotWorker,DashboardWorker\" > \"/opt/Wyn/.modules\"
    INSTALL_SERVER=true
    INSTALL_REPORTING_WORKER=true
  fi

  # copy resources
  if \$INSTALL_REPORTING_WORKER && [ ! -d \"/usr/share/fonts/liberation-fonts-ttf-2.00.1\" ] ; then
    mkdir -p \"/usr/share/fonts\"
    tar zxf \"/opt/Wyn/liberation-fonts-ttf-2.00.1.tar.gz\" -C \"/usr/share/fonts\" > /dev/null 2>/dev/null
  fi
  
  # install MonetDB
  if \$INSTALL_SERVER ; then
    needUninstallMonetDB=false
    needInstallMonetDB=false
    if [ ! -f \"/opt/Wyn/conf/Wyn.conf\" ]; then
      monetdbd --version > /dev/null 2>/dev/null
      if [ \"0\" == \"\$?\" ]; then
        # MonetDB Database Server v11.37.11 (Jun2020-SP1)
        targetVersion=\"11.43.23\"
        text=\$(monetdbd --version)
        version=\$(echo \"\$text\" | cut -c 26-33)
        currentVersion=\$(echo \"\${version//[[:space:]]/}\")
        for idx in 1 2 3
        do
          v1=\$(echo \"\$currentVersion\" | cut -d '.' -f \$idx)
          v2=\$(echo \"\$targetVersion\" | cut -d '.' -f \$idx)
          if [ \"\$v1\" -lt \"\$v2\" ]; then
            needUninstallMonetDB=true
            needInstallMonetDB=true
            break
          fi
        done
      else
        needInstallMonetDB=true
      fi
    fi
    
    if \$needUninstallMonetDB ; then
      # remove monetdb
      echo \"Removing old version MonetDB...\"
      if [ -f \"/lib/systemd/system/monetdbd.service\" ]; then
        rm \"/lib/systemd/system/monetdbd.service\"
      fi
      if [ -f \"/usr/lib/systemd/system/monetdbd.service\" ]; then
        rm \"/usr/lib/systemd/system/monetdbd.service\"
      fi
      if [ -L \"/etc/systemd/system/multi-user.target.wants/monetdbd.service\" ]; then
        rm \"/etc/systemd/system/multi-user.target.wants/monetdbd.service\"
      fi
      rm -rf /etc/logrotate.d/monetdbd > /dev/null 2>/dev/null
      rm -rf /run/monetdb > /dev/null 2>/dev/null
      rm -rf /usr/bin/mclient > /dev/null 2>/dev/null
      rm -rf /usr/bin/monetdb > /dev/null 2>/dev/null
      rm -rf /usr/bin/monetdbd > /dev/null 2>/dev/null
      rm -rf /usr/bin/mserver5 > /dev/null 2>/dev/null
      rm -rf /usr/bin/msqldump > /dev/null 2>/dev/null
      rm -rf /usr/lib64/monetdb5 > /dev/null 2>/dev/null
      rm -rf /usr/lib64/libmapi.* > /dev/null 2>/dev/null
      rm -rf /usr/lib64/libmonetdb* > /dev/null 2>/dev/null
      rm -rf /usr/share/doc/MonetDB* > /dev/null 2>/dev/null
      rm -rf /usr/share/licenses/MonetDB* > /dev/null 2>/dev/null
      rm -rf /usr/share/man/man1/mclient.1.gz > /dev/null 2>/dev/null
      rm -rf /usr/share/man/man1/monetdb.1.gz > /dev/null 2>/dev/null
      rm -rf /usr/share/man/man1/monetdbd.1.gz > /dev/null 2>/dev/null
      rm -rf /usr/share/man/man1/mserver5.1.gz > /dev/null 2>/dev/null
      rm -rf /usr/share/man/man1/msqldump.1.gz > /dev/null 2>/dev/null
      rm -rf /var/lib/monetdb > /dev/null 2>/dev/null
      rm -rf /var/log/monetdb > /dev/null 2>/dev/null
    fi
    if \$needInstallMonetDB ; then
      # install monetdb
      # the .sh file cannot be found here...
      echo \"Installing MonetDB...\"
      rm -rf /tmp/monetdb > /dev/null 2>/dev/null
      tar zxf /opt/Wyn/monetdb-11.43.23.tar.gz -C /tmp/
      zypper --version > /dev/null 2>/dev/null
      if [ \"0\" == \"\$?\" ]; then
        echo \"[OS:openSUSE15]\"
        cp -rf /tmp/monetdb/suse/15/monetdb/* /
      else
        elVer=\$(cat /etc/os-release | grep PLATFORM_ID | sed 's/[^0-9]//g')
        if [ \"\" == \"\$elVer\" ]; then
          echo \"[OS:EPEL7]\"
          cp -rf /tmp/monetdb/epel/7/monetdb/* /
        else
          if [ -d /tmp/monetdb/epel/\$elVer ]; then
            echo \"[OS:EPEL\$elVer]\"
            cp -rf /tmp/monetdb/epel/\$elVer/monetdb/* /
          else
            echo \"Won't install MonetDB on this server since unsupported OS version detected, the version is 'epel:\$elVer'.\"
          fi
        fi
      fi
      
      if ! getent group monetdb > /dev/null; then
        groupadd --system monetdb
      fi
      if ! getent passwd monetdb > /dev/null; then
        useradd --system --gid monetdb --home-dir /var/lib/monetdb --shell /sbin/nologin --comment \"MonetDB Server\" monetdb
      fi
      mkdir -p /var/monetdb5/dbfarm
      chown -R monetdb:monetdb /var/monetdb5
      chmod ug=rwx,g+s,o= /var/monetdb5 /var/monetdb5/dbfarm
      mkdir -p /var/lib/monetdb
      chown -R monetdb:monetdb /var/lib/monetdb
      chmod ug=rwx,g+s,o= /var/lib/monetdb
      mkdir -p /var/log/monetdb /run/monetdb
      chown -R monetdb:monetdb /var/log/monetdb /run/monetdb
      chmod ug=rwx,o= /var/log/monetdb
      chmod ug=rwx,o=rx /run/monetdb
      chown monetdb:monetdb /usr/bin/mclient
      chown monetdb:monetdb /usr/bin/monetdb
      chown monetdb:monetdb /usr/bin/monetdbd
      chown monetdb:monetdb /usr/bin/mserver5
      chown monetdb:monetdb /usr/bin/msqldump
      chmod +x /usr/bin/mclient
      chmod +x /usr/bin/monetdb
      chmod +x /usr/bin/monetdbd
      chmod +x /usr/bin/mserver5
      chmod +x /usr/bin/msqldump
      echo \"MonetDB installed successfully.\"
      cd - > /dev/null 2>/dev/null
    fi
  fi
  # backup configuration file
  if [ -f \"/opt/Wyn/conf/Wyn.conf\" ]; then
    cp \"/opt/Wyn/conf/Wyn.conf\" \"/opt/Wyn/conf/Wyn.conf.$(date +\"%Y%m%d%H%M%S\")\"
  elif [ -f \"/opt/Wyn/Monitor/conf/Wyn.conf\" ]; then
    cp \"/opt/Wyn/Monitor/conf/Wyn.conf\" \"/opt/Wyn/Monitor/conf/Wyn.conf.$(date +\"%Y%m%d%H%M%S\")\"
  fi
  # install JRE
  if \$INSTALL_SERVER && [ ! -f \"/opt/Wyn/conf/Wyn.conf\" ] && [ ! -d \"/opt/Wyn/jre\" ] ; then
    tar zxf \"/opt/Wyn/jre8.tar.gz\" -C \"/opt/Wyn/\"
  fi
  # remove useless files
  echo \"removing useless files...\"
  rm -rf \"/opt/Wyn/jre8.tar.gz\" > /dev/null 2>/dev/null
  rm -rf \"/opt/Wyn/services\" > /dev/null 2>/dev/null
  rm -rf \"/opt/Wyn/liberation-fonts-ttf-2.00.1.tar.gz\" > /dev/null 2>/dev/null
  rm -rf \"/opt/Wyn/monetdb-11.43.23.tar.gz\" > /dev/null 2>/dev/null
  rm -rf \"/opt/Wyn/monetdb\" > /dev/null 2>/dev/null
  rm -rf \"/tmp/monetdb\" > /dev/null 2>/dev/null
fi
exit 0

%preun
echo \"pre-removal checking...\"
if [ \$1 == 0 -o \$1 == 1 ]; then
  if [ -f /lib/systemd/system/wyn.service ] || [ -f /usr/lib/systemd/system/wyn.service ]; then
    systemctl is-active -q wyn > /dev/null 2>/dev/null
    if [ \"0\" == \"\$?\" ]; then
      echo \"Stopping wyn.service...\"
      systemctl stop wyn > /dev/null 2>/dev/null
      systemctl stop wyn-datasource.service > /dev/null 2>/dev/null
    fi
  elif [ -f /etc/init.d/wyn ]; then
    service wyn status > /dev/null 2>/dev/null
    if [ \"0\" == \"\$?\" ]; then
      echo \"Stopping service wyn...\"
      service wyn stop > /dev/null 2>/dev/null
      service wyn-datasource stop > /dev/null 2>/dev/null
    fi
  fi
fi
exit 0

%postun
echo \"post-removal checking...\"
if [ \$1 == 0 ]; then
  # remove wyn.service
  if [ -L /etc/systemd/system/multi-user.target.wants/wyn.service ]; then
    rm /etc/systemd/system/multi-user.target.wants/wyn.service
  fi
  if [ -f /lib/systemd/system/wyn.service ]; then
    rm /lib/systemd/system/wyn.service
    systemctl daemon-reload
  fi
  if [ -f /usr/lib/systemd/system/wyn.service ]; then
    rm /usr/lib/systemd/system/wyn.service
    systemctl daemon-reload
  fi
  if [ -f /etc/init.d/wyn ]; then
    rm /etc/init.d/wyn
  fi
  rm /opt/Wyn/.modules > /dev/null 2>/dev/null
  rm -rf /opt/Wyn/Monitor/conf > /dev/null 2>/dev/null
  rm -rf /opt/Wyn/Tools/RegisterTrialInfo > /dev/null 2>/dev/null
  # remove wyn-database.service
  systemctl stop wyn-database > /dev/null 2>/dev/null
  if [ -L /etc/systemd/system/multi-user.target.wants/wyn-database.service ]; then
    rm /etc/systemd/system/multi-user.target.wants/wyn-database.service
  fi
  if [ -f /lib/systemd/system/wyn-database.service ]; then
    rm /lib/systemd/system/wyn-database.service
    systemctl daemon-reload
  fi
  if [ -f /usr/lib/systemd/system/wyn-database.service ]; then
    rm /usr/lib/systemd/system/wyn-database.service
    systemctl daemon-reload
  fi
  # remove jre
  rm -rf /opt/Wyn/jre > /dev/null 2>/dev/null
  # remove monetdb
  echo \"Removing MonetDB...\"
  if [ -f \"/lib/systemd/system/monetdbd.service\" ]; then
    rm \"/lib/systemd/system/monetdbd.service\"
  fi
  if [ -f \"/usr/lib/systemd/system/monetdbd.service\" ]; then
    rm \"/usr/lib/systemd/system/monetdbd.service\"
  fi
  if [ -L \"/etc/systemd/system/multi-user.target.wants/monetdbd.service\" ]; then
    rm \"/etc/systemd/system/multi-user.target.wants/monetdbd.service\"
  fi
  rm -rf /etc/logrotate.d/monetdbd > /dev/null 2>/dev/null
  rm -rf /run/monetdb > /dev/null 2>/dev/null
  rm -rf /usr/bin/mclient > /dev/null 2>/dev/null
  rm -rf /usr/bin/monetdb > /dev/null 2>/dev/null
  rm -rf /usr/bin/monetdbd > /dev/null 2>/dev/null
  rm -rf /usr/bin/mserver5 > /dev/null 2>/dev/null
  rm -rf /usr/bin/msqldump > /dev/null 2>/dev/null
  rm -rf /usr/lib64/monetdb5 > /dev/null 2>/dev/null
  rm -rf /usr/lib64/libmapi.* > /dev/null 2>/dev/null
  rm -rf /usr/lib64/libmonetdb* > /dev/null 2>/dev/null
  rm -rf /usr/share/doc/MonetDB* > /dev/null 2>/dev/null
  rm -rf /usr/share/licenses/MonetDB* > /dev/null 2>/dev/null
  rm -rf /usr/share/man/man1/mclient.1.gz > /dev/null 2>/dev/null
  rm -rf /usr/share/man/man1/monetdb.1.gz > /dev/null 2>/dev/null
  rm -rf /usr/share/man/man1/monetdbd.1.gz > /dev/null 2>/dev/null
  rm -rf /usr/share/man/man1/mserver5.1.gz > /dev/null 2>/dev/null
  rm -rf /usr/share/man/man1/msqldump.1.gz > /dev/null 2>/dev/null
  rm -rf /var/lib/monetdb > /dev/null 2>/dev/null
  rm -rf /var/log/monetdb > /dev/null 2>/dev/null
fi
exit 0

%posttrans
if [ -f \"/opt/Wyn/.upgrade\" ]; then
  # starting service
  echo \"Starting service wyn.service...\"
  systemctl --version > /dev/null 2>/dev/null
  if [ \"0\" == \"\$?\" ]; then
    systemctl start wyn.service
    systemctl start wyn-datasource.service > /dev/null 2>/dev/null
  else
    service wyn start
    service wyn-datasource start > /dev/null 2>/dev/null
  fi

  # remove upgrade file
  rm \"/opt/Wyn/.upgrade\"

  # wait for service startup
  sleep 30
  echo \"Finished.\"
fi
exit 0
" > $SpecFile

# build RPM package
echo "build RPM package"
rpmbuild --quiet -bb $SpecFile

sleep 30

# copy RPM package to azure storage
if [ -f "$RpmBuildDir/RPMS/x86_64/$RpmFileName" ]; then
  echo "copy RPM package to azure storage"
  azcopy copy "$RpmBuildDir/RPMS/x86_64/$RpmFileName" "$RepoURL/$RpmFileName?$RepoSAS"
  rm -rf $RpmBuildDir/BUILDROOT/* > /dev/null 2>&1
  rm -rf $RpmBuildDir/RPMS/x86_64/* > /dev/null 2>&1
  rm -rf $RpmBuildDir/SPECS/* > /dev/null 2>&1
  rm -rf $RpmBuildDir/tmp/* > /dev/null 2>&1
  rm -rf $WynDir > /dev/null 2>&1
  echo "finished"
  exit 0
else
  echo "build RPM package failed, no RPM file generated!"
  rm -rf $WynDir > /dev/null 2>&1
  exit 1
fi
