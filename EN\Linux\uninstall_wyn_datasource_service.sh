#!/bin/bash

WYN_DATASOURCE_SERVICE="wyn-datasource.service"
SERVICE_FILE_LOCATION="/lib/systemd/system"
if [ ! -d "$SERVICE_FILE_LOCATION" ]; then
  SERVICE_FILE_LOCATION="/usr/lib/systemd/system"
fi

if [ -f "$SERVICE_FILE_LOCATION/$WYN_DATASOURCE_SERVICE" ]; then
  sudo systemctl stop $WYN_DATASOURCE_SERVICE > /dev/null 2>&1
  sudo systemctl disable $WYN_DATASOURCE_SERVICE > /dev/null 2>&1
  sudo rm -rf "$SERVICE_FILE_LOCATION/$WYN_DATASOURCE_SERVICE" > /dev/null 2>&1
  sudo systemctl daemon-reload > /dev/null 2>&1
fi

echo "Uninstall Wyn datasource service 'wyn-datasource' successfully."

exit 0
