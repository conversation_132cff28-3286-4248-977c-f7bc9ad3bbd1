﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace gces_language_package_bundler
{
	internal class Program
	{
		static async Task<int> Main(string[] args)
		{
			var validationResult = ArgumentsValidator.ValidateAndParseArguments(args);
			if (!validationResult.IsValid)
			{
				Console.WriteLine(validationResult.ErrorMessage);
				Environment.Exit(-1);
			}

			Console.WriteLine(
				$"Parameters: {validationResult.ManifestFilePath} {validationResult.ArtifactsDir} {validationResult.WynVersion} {validationResult.IsEnEdition} {validationResult.OrderConfigDir}"
			);

			var targetFiles = ParseTargetFiles(
				validationResult.ManifestFilePath,
				validationResult.ArtifactsDir
			);
			var exclusiveConfigs = LoadExclusiveConfigs();

			var languageValidationResult = await LanguageResourcesValidator.ValidateLanguageResources(validationResult.ArtifactsDir);
			if (!languageValidationResult.ValidationDetails.Valid)
			{
				LanguageResourcesValidator.PrintValidationErrors(languageValidationResult.ValidationDetails);
				Environment.Exit(1);
			}

			var resourceOrderManager = new ResourceOrderManager(validationResult.OrderConfigDir);
			BundleLanguagePackages(
				validationResult,
				languageValidationResult.LanguageArtifacts,
				targetFiles,
				exclusiveConfigs,
				resourceOrderManager
			);

			Console.WriteLine("bundle language packages end ...");
			Environment.Exit(0);
			return 0;
		}

		private static List<(string RepoName, string OutputFile)> ParseTargetFiles(
			string manifestFilePath,
			string artifactsDir
		)
		{
			return File.ReadLines(manifestFilePath, Encoding.UTF8)
				.Where(line => !string.IsNullOrEmpty(line) && line.Contains("-languages"))
				.Select(repoNameWithArtifact =>
				{
					var segs = repoNameWithArtifact
						.Split(new char[] { ' ', '\t', '\n' })
						.Where(s => !string.IsNullOrEmpty(s))
						.Select(s => s.Trim())
						.ToList();
					var repoName = segs[0];
					var artifact = segs[2];
					var targetFile = Path.Combine(artifactsDir, artifact);
					return (RepoName: repoName, OutputFile: targetFile);
				})
				.ToList();
		}

		private static List<ExclusiveConfig> LoadExclusiveConfigs()
		{
			try
			{
				string assemblyFolder = Path.GetDirectoryName(
					Assembly.GetExecutingAssembly().Location
				);
				var configFilePath = Path.Combine(assemblyFolder, "exclusive-config.json");
				if (!File.Exists(configFilePath))
				{
					Console.WriteLine("bundle language package error. relative path not exist");
					Environment.Exit(1);
				}

				var exclusiveConfigStr = File.ReadAllText(configFilePath);
				return JsonSerializer.Deserialize<List<ExclusiveConfig>>(
					exclusiveConfigStr,
					new JsonSerializerOptions() { PropertyNameCaseInsensitive = true }
				);
			}
			catch
			{
				Console.WriteLine("bundle language package error. exclusive config parse error");
				Environment.Exit(1);
				return null;
			}
		}

		private static bool ValidateLanguageResources(LngResourceConfigValidateRes validateRes)
		{
			if (!validateRes.Valid)
			{
				LanguageResourcesValidator.PrintValidationErrors(validateRes);
				return false;
			}

			return true;
		}

		private static void BundleLanguagePackages(
			ArgumentsValidationResult validationResult,
			List<string> languageArtifacts,
			List<(string RepoName, string OutputFile)> targetFiles,
			List<ExclusiveConfig> exclusiveConfigs,
			ResourceOrderManager resourceOrderManager
		)
		{
			var langPackLng = new List<string>() { "en", "zh", "zh-TW", "pl" };

			foreach (var lng in langPackLng)
			{
				var tempPackName = Utils.BundleLanguagePackage(
					lng,
					validationResult.ArtifactsDir,
					languageArtifacts,
					targetFiles
				);
				var edition = validationResult.IsEnEdition ? "en" : "cn";
				var packName = tempPackName + $"-{edition.ToUpper()}.zip";
				File.Move($"{tempPackName}.zip", packName, true);
				Utils.AddManifest(packName, edition, lng, validationResult.WynVersion);
				Utils.RemoveExclusivePlugins(packName, edition, exclusiveConfigs);
				resourceOrderManager.SaveResourceOrder(
					packName,
					lng,
					languageArtifacts,
					targetFiles
				);
			}
		}
	}
}
