#!/bin/bash

#====================================== GLOBAL VARIABLES ======================================#

INSTALL_TARGET_DIR="/opt"
WYN_INSTALL_DIR="$INSTALL_TARGET_DIR/Wyn"

CLUSTER_MODE=false

MODULE_SERVER="Server"
MODULE_REPORTING_WORKER="ReportingWorker"
MODULE_COT_WORKER="CotWorker"
MODULE_DASHBOARD_WORKER="DashboardWorker"

MODULES=""
WYN_VERSION=""
SILENT_INSTALLATION=false
LOG_FILE="install.log"
LICENSE=""
CONFIGURE_SCRIPT_FILE="$WYN_INSTALL_DIR/Monitor/configure.sh"
DEFAULT_LANGUAGE="English"

EXIT_CODE_SUCCESS=0
EXIT_CODE_SHOW_HELP=1
EXIT_CODE_INVALID_ARGUMENTS=2
EXIT_CODE_INVALID_DATABASE_PROVIDER=3
EXIT_CODE_INVALID_DATABASE_CONNECTION_STRING=4
EXIT_CODE_INVALID_MYSQL_REQUIREMENT=5
EXIT_CODE_INVALID_MODULES_DETECTED=6
EXIT_CODE_NO_MODULES_SELECTED=7
EXIT_CODE_NO_BACKEND_SERVER_ADDRESS=8
EXIT_CODE_START_WYN_FAILED=9
EXIT_CODE_REGISTER_LICENSE_FAILED=10
EXIT_CODE_INVALID_PASSWORD=11
EXIT_CODE_UNSUPPORTED_POSTGRES_VERSION=12
EXIT_CODE_NO_CONFIGURE_SCRIPT=30
EXIT_CODE_CTRL_C=100

#====================================== FUNCTIONS ======================================#

prtMsg() {
  for msg in "$@"
  do
    if $SILENT_INSTALLATION ; then
      echo "$(date) $msg" >> $LOG_FILE
    else
      echo "$msg"
    fi
  done
}

# catch "ctrl-c" and exit the installation process
trap_ctrlc() {
  prtMsg ""
  prtMsg "Ctrl-C caught, canceling the installation..."
  stty echo
  exit $EXIT_CODE_CTRL_C
}

parseArguments() {
  OPTIND=1
  local ret=0
  while getopts sd:c:om:if:b:k:a:v:t:L:elh opt; do
    case $opt in
      s)
        SILENT_INSTALLATION=true
        ;;
      d)
        DB_PROVIDER="$OPTARG"
        ;;
      c)
        DATABASE_CONNECTIONSTRING="$OPTARG"
        ;;
      o)
        SINGLE_DATABASE_MODE=true
        ;;
      m)
        MODULES="$OPTARG"
        ;;
      i)
        IMPORT_SAMPLES=true
        ;;
      f)
        SAMPLE_FILE="$OPTARG"
        ;;
      b)
        BACKEND_SERVER_ADDRESS="$OPTARG"
        ;;
      k)
        LICENSE="$OPTARG"
        ;;
      a)
        ADMIN_PASSWORD="$OPTARG"
        ;;
      v)
        WYN_VERSION="$OPTARG"
        ;;
      t)
        SECONDARY_NODE_SECRET="$OPTARG"
        ;;
      L)
        DEFAULT_LANGUAGE="$OPTARG"
        ;;
      e)
        ENCRYPT_DATABASE_CONNECTION_STRING=true
        ;;
      l)
        CLUSTER_MODE=true
        CONFIG_FILE_PATH="$WYN_INSTALL_DIR/Monitor/conf/Wyn.conf"
        ;;
      h)
        ret=$EXIT_CODE_SHOW_HELP
        ;;
      *)
        ret=$EXIT_CODE_INVALID_ARGUMENTS
        ;;
    esac
  done
  return $ret
}

showUsage() {
  prtMsg "Usage: ${0##*/} [-s] [-d {DATABASE_PROVIDER} -c {DATABASE_CONNECTION_STRING}] [-o] [-m {MODULES}] [-i [-f {SAMPLE_FILE}]] [-b {BACKEND_SERVER_ADDRESS}] [-k {LICENSE_KEY}] [-a {ADMIN_PASSWORD}] [-v {VERSION}] [-t {SECONDARY_NODE_SECRET}] [-L {DEFAULT_LANGUAGE}] [-e {ENCRYPT_DATABASE_CONNECTION_STRING}] [-l {NON_SINGLE_PROCESS_MODE}] [-h]
  -s    Silent installation.
  -d    Database provider, available values: 'Postgres', 'SqlServer', 'MySql' and 'Oracle'.
  -c    Database server connection string.
  -o    Single database mode.
  -m    Modules to be installed, including '$MODULE_SERVER', '$MODULE_REPORTING_WORKER', '$MODULE_COT_WORKER' and '$MODULE_DASHBOARD_WORKER'.
  -i    Import samples.
  -f    The location of the user specified sample data.
  -b    The back-end server address.
  -k    The license key to register to Wyn system.
  -a    The password of the administrator user 'admin'.
  -v    Wyn version.
  -t    The secret for secondary node.
  -L    The default language, available values: 'en-US', and 'ko-KR'.
  -e    Encrypt the database connection string.
  -l    Non single-process mode.
  -h    Help information."
}

# input: $1 - Wyn version
installWyn() {
  local wynVersion=$1
  local repoDir="/etc/zypp/repos.d"
  local isYumAvailable=false
  yum --version > /dev/null 2>&1
  if [ "0" == "$?" ]; then
    isYumAvailable=true
    repoDir="/etc/yum.repos.d"
  fi

  # write repository file
  if [ ! -f "$repoDir/wyn-9.0.repo" ]; then
    sudo bash -c 'echo "[wyn-9.0]
name=Wyn Enterprise 9.0
baseurl=https://cdn.wynenterprise.io/BI/installation/yum/9.0/
enabled=1
gpgcheck=0" > wyn-9.0.repo'
    sudo mv wyn-9.0.repo "$repoDir"
  fi

  if $SILENT_INSTALLATION ; then
    if [ "" == "$wynVersion" ]; then
      if $isYumAvailable ; then
        sudo yum install -y wyn-enterprise >> $LOG_FILE 2>&1
      else
        sudo zypper install -y wyn-enterprise >> $LOG_FILE 2>&1
      fi
    else
      if $isYumAvailable ; then
        sudo yum install -y wyn-enterprise-$wynVersion >> $LOG_FILE 2>&1
      else
        sudo zypper install -y wyn-enterprise=$wynVersion >> $LOG_FILE 2>&1
      fi
    fi
  else
    if [ "" == "$wynVersion" ]; then
      if $isYumAvailable ; then
        sudo yum install wyn-enterprise
      else
        sudo zypper install wyn-enterprise
      fi
    else
      if $isYumAvailable ; then
        sudo yum install wyn-enterprise-$wynVersion
      else
        sudo zypper install wyn-enterprise=$wynVersion
      fi
    fi
  fi
}

#====================================== INSTALLATION ======================================#

trap "trap_ctrlc" INT

# parse and check the parameters
parseArguments "$@"
exit_code=$?
if [ "0" != "$exit_code" ]; then
  showUsage
  exit $exit_code
fi

if $SILENT_INSTALLATION ; then
  prtMsg "-------------------- INSTALLATION START --------------------"
fi

# install Wyn
installWyn $WYN_VERSION

# import samples
if ! $IMPORT_SAMPLES ; then
  prtMsg "Removing the built-in sample files..."
  sudo rm -rf "$WYN_INSTALL_DIR/sampledata/" > /dev/null 2>/dev/null
  sudo rm -rf "$WYN_INSTALL_DIR/$MODULE_SERVER/sample_files/" > /dev/null 2>/dev/null
elif [ "" != "$SAMPLE_FILE" ]; then
  sudo rm -rf "$WYN_INSTALL_DIR/sampledata/" > /dev/null 2>/dev/null
  sudo rm -rf "$WYN_INSTALL_DIR/$MODULE_SERVER/sample_files/"* > /dev/null 2>/dev/null
  sudo rm -rf "$WYN_INSTALL_DIR/$MODULE_SERVER/sample_files/.finished" > /dev/null 2>/dev/null
  if [ -f "$SAMPLE_FILE" ]; then
    sudo mkdir -p "$WYN_INSTALL_DIR/$MODULE_SERVER/sample_files" > /dev/null 2>/dev/null
    sudo cp "$SAMPLE_FILE" "$WYN_INSTALL_DIR/$MODULE_SERVER/sample_files/sample_files.zip" > /dev/null 2>/dev/null
  else
    IMPORT_SAMPLES=false
    prtMsg "The sample data '{$SAMPLE_FILE}' does not exist, so no sample data will be imported."
  fi
fi

# configure service
ret=$EXIT_CODE_SUCCESS

sleep 5

sudo systemctl is-active -q wyn > /dev/null 2>/dev/null
if [ "0" != "$?" ]; then
  if [ -f "$CONFIGURE_SCRIPT_FILE" ]; then
    sudo bash "$CONFIGURE_SCRIPT_FILE" "$@"
    ret=$?
  else
    ret=$EXIT_CODE_NO_CONFIGURE_SCRIPT
  fi
elif $IMPORT_SAMPLES ; then
  sudo systemctl restart wyn > /dev/null 2>/dev/null
  ret=$?
fi

exit $ret
