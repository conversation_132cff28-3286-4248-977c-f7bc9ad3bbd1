using System;
using System.IO;

namespace gces_language_package_bundler
{
    public class ArgumentsValidationResult
    {
        public bool IsValid { get; set; }
        public string ManifestFilePath { get; set; }
        public string ArtifactsDir { get; set; }
        public string WynVersion { get; set; }
        public bool IsEnEdition { get; set; }
        public string OrderConfigDir { get; set; }
        public string ErrorMessage { get; set; }
    }

    internal class ArgumentsValidator
    {
        public static ArgumentsValidationResult ValidateAndParseArguments(string[] args)
        {
            var result = new ArgumentsValidationResult();

            if (args == null || args.Length < 5)
            {
                result.IsValid = false;
                result.ErrorMessage = "Usage: dotnet gces-language-package-bundler.dll {manifest_file_location} {artifacts_dir} {wyn_version} {is_en_edition} {order_config_dir}";
                return result;
            }

            result.ManifestFilePath = args[0];
            result.ArtifactsDir = args[1];
            result.WynVersion = args[2];

            if (!bool.TryParse(args[3], out var isEnEdition))
            {
                result.IsValid = false;
                result.ErrorMessage = "is_en_edition parameter must be a boolean value (true/false)";
                return result;
            }
            result.IsEnEdition = isEnEdition;

            result.OrderConfigDir = args[4];

            if (!File.Exists(result.ManifestFilePath))
            {
                result.IsValid = false;
                result.ErrorMessage = $"Manifest file not found: {result.ManifestFilePath}";
                return result;
            }

            if (!Directory.Exists(result.ArtifactsDir))
            {
                result.IsValid = false;
                result.ErrorMessage = $"Artifacts directory not found: {result.ArtifactsDir}";
                return result;
            }

            if (!Directory.Exists(result.OrderConfigDir))
            {
                try
                {
                    Directory.CreateDirectory(result.OrderConfigDir);
                }
                catch (Exception ex)
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"Failed to create order config directory: {result.OrderConfigDir}. Error: {ex.Message}";
                    return result;
                }
            }

            result.IsValid = true;
            return result;
        }
    }
} 