[{"Module": "Scheduler", "DisplayName": "Schedule Task", "Order": 1, "Obsolete": false, "Path": "gces-server/src/Gces.Scheduler.Plugin/clientApp/src/localization/en.ts"}, {"Module": "Scheduler", "DisplayName": "Dashboard Task", "Order": 2, "Obsolete": false, "Path": "gces-server/src/Gces.Scheduler.Plugin/clientApp/src/localization/dashboard/scheduling.en.ts"}, {"Module": "Shared Resources", "DisplayName": "Floor Plan 1", "Order": 3, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/floorPlanDesigner/localization/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Floor Plan 2", "Order": 4, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/floorPlanDesigner/localization/portalLocaleData/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Hierarchical Map 1", "Order": 5, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/geoJson/localization/en.json"}, {"Module": "Shared Resources", "DisplayName": "Hierarchical Map 2", "Order": 6, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/geoJson/localization/portalLocaleData/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Document Theme 1", "Order": 7, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/themeDesigner/localization/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Document Theme 2", "Order": 8, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/themeDesigner/localization/portalLocaleData/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Document Theme Viewer", "Order": 9, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/themePreview/localization/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Visualization 1", "Order": 10, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/visual/localization/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Visualization 2", "Order": 11, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/visual/localization/portalLocaleData/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Web Page 1", "Order": 12, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/webPages/localization/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Web Page 2", "Order": 13, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/webPages/localization/portalLocaleData/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Font 1", "Order": 14, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/font/localization/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Font 2", "Order": 15, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/font/localization/portalLocaleData/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Language Resource 1", "Order": 16, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResource/localization/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Language Resource 2", "Order": 17, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResource/localization/portalLocaleData/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Language Resource Viewer 1", "Order": 18, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResourcePreview/localization/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Language Resource Viewer 2", "Order": 19, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResourcePreview/localization/portalLocaleData/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Document Binders Viewer 1", "Order": 20, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBindersPreview/localization/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Document Binders Viewer 2", "Order": 21, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBindersPreview/localization/portalLocaleData/en.ts"}, {"Module": "Portal", "DisplayName": "Error <PERSON>", "Order": 22, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Error/localization/en.ts"}, {"Module": "Admin Portal", "DisplayName": "Design Shell Component", "Order": 23, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/AdminPortal/localization/designer-shell/en.json"}, {"Module": "Portal", "DisplayName": "Admin Portal", "Order": 24, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/AdminPortal/localization/en.json"}, {"Module": "Portal", "DisplayName": "Portal Common Info", "Order": 25, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/en.json"}, {"Module": "Portal", "DisplayName": "Category", "Order": 26, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/TagsPanel/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Document Types", "Order": 27, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/ResourcePanel/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Category Editor", "Order": 28, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/TagsEditor/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Work Space", "Order": 29, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/WorkspaceBase/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Upload", "Order": 30, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/Upload/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Search(mobile portal)", "Order": 31, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/MobileSearch/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Resource Portal", "Order": 32, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/EN/ResourcePortal/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Document Portal 1", "Order": 33, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/EN/DocumentPortal/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Document Portal 2", "Order": 34, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/ZH/DocumentPortal/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Mobile Portal", "Order": 35, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/ZH/MobilePortal/localization/en.ts"}, {"Module": "Admin Portal", "DisplayName": "System Diagnostic", "Order": 36, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/SystemDiagnostic/localization/en.ts"}, {"Module": "Admin Portal", "DisplayName": "Node Management", "Order": 37, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/NodeManagement/localization/en.json"}, {"Module": "Admin Portal", "DisplayName": "<PERSON>t Log", "Order": 38, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/AuditLogConfiguration/localization/en.json"}, {"Module": "Portal", "DisplayName": "Copy Url Dialog", "Order": 39, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/CopyUrlDialog/localization/en.ts"}, {"Module": "Admin Portal", "DisplayName": "Document Permission Editor", "Order": 40, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/DocsPermissionEditor/localization/en.json"}, {"Module": "Admin Portal", "DisplayName": "Document Category Editor", "Order": 41, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/DocumentsTagsEditor/localization/en.ts"}, {"Module": "Admin Portal", "DisplayName": "External Storage", "Order": 42, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/ExternalStorage/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Personal Category", "Order": 43, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/PersonalTagManagement/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Profile Setting", "Order": 44, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/ProfileEditor/localization/en.ts"}, {"Module": "Admin Portal", "DisplayName": "Schedule Templates", "Order": 45, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/Schedule/localization/en.ts"}, {"Module": "Admin Portal", "DisplayName": "System Management", "Order": 46, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/SystemManagement/localization/en.json"}, {"Module": "Admin Portal", "DisplayName": "Task Monitor", "Order": 47, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/WorkerTiles/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Recycle Bin", "Order": 48, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/SoftDeletedDocument/localization/en.ts"}, {"Module": "Data Monitoring", "DisplayName": "Data Monitoring 1", "Order": 49, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DataMonitoring/localization/en.ts"}, {"Module": "Data Monitoring", "DisplayName": "Data Monitoring 2", "Order": 50, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DataMonitoring/localization/editor/en.json"}, {"Module": "Portal", "DisplayName": "Document Draft 1", "Order": 51, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DocumentDraftList/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Document Draft 2", "Order": 52, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DocumentDraftPanel/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Document Draft 3", "Order": 53, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/PublishRequestList/localization/en.ts"}, {"Module": "Admin Portal", "DisplayName": "File Extraction Setting", "Order": 54, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/FileDataSourceConfiguration/localization/en.ts"}, {"Module": "Document Order", "DisplayName": "Document Type", "Order": 55, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/DocumentTypesLocaleData/en.json"}, {"Module": "Document Order", "DisplayName": "Document Extension", "Order": 56, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/DocumentExtLocaleData/en.json"}, {"Module": "API", "DisplayName": "Common Errors(Server Plugin)", "Order": 57, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/ApiErrorsLocaleData/v2/en.json"}, {"Module": "Portal", "DisplayName": "Designer Work Sheets", "Order": 58, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/Designer/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Preview Tabs", "Order": 59, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/PreviewTabs/localization/en.ts"}, {"Module": "Portal", "DisplayName": "Document Binders", "Order": 60, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBinder/localization/portalLocalData/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Document Binders designer", "Order": 61, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBinder/localization/en.ts"}, {"Module": "Identity", "DisplayName": "License", "Order": 62, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/license/localization/en.ts"}, {"Module": "Identity", "DisplayName": "<PERSON><PERSON>", "Order": 63, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/login/localization/en.ts"}, {"Module": "Identity", "DisplayName": "Login <PERSON>", "Order": 64, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/login-to-tenant/localization/en.ts"}, {"Module": "Identity", "DisplayName": "Trial Info Register", "Order": 65, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/trial-info-register/localization/en.ts"}, {"Module": "Identity", "DisplayName": "Verification Code Page", "Order": 66, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/verification/localization/en.ts"}, {"Module": "API", "DisplayName": "Common Errors(Dataset Plugin)", "Order": 67, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Dataset Type", "Order": 68, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/documentSectionRegisterLocaleData/en.ts"}, {"Module": "Dataset", "DisplayName": "Parameter Panel", "Order": 69, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/previewParameterRegisterLocaleData/en.ts"}, {"Module": "Dataset", "DisplayName": "Message Box", "Order": 70, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/MessageBox/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Notification", "Order": 71, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/notifications/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Push and Streaming Dataset", "Order": 72, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/singleTableDataset/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Dataset Cache", "Order": 73, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/CotManagement/localization/en.ts"}, {"Module": "Data Source", "DisplayName": "Data Providers", "Order": 74, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DataProviders/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Cache Dataset", "Order": 75, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasetDesigner/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Streaming Dataset", "Order": 76, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/StreamingDatasetDesigner/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Push Dataset", "Order": 77, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/PushDatasetDesigner/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Native Query Dataset 1", "Order": 78, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/NativeQueryDatasetDesigner/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Native Query Dataset 2", "Order": 79, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/NativeQueryDataset/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Dataset Sidebar", "Order": 80, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasetSidebarTab/localization/en.ts"}, {"Module": "Data Source", "DisplayName": "Data Source Designer", "Order": 81, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasourceDesigner/localization/en.json"}, {"Module": "Data Source", "DisplayName": "Data Source Sidebar", "Order": 82, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasourceSidebarTab/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Dataset Viewer", "Order": 83, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/PreviewDataset/localization/en.ts"}, {"Module": "Data Source", "DisplayName": "Data Source Viewer", "Order": 84, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/PreviewDatasource/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Dataset Basic Info", "Order": 85, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/localization/portal.en.json"}, {"Module": "Dataset", "DisplayName": "Prepare Data 1", "Order": 86, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/creations/PrepareData/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Prepare Data 2", "Order": 87, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/creations/PrepareData/localization/portalRegisterLocaleData/en.ts"}, {"Module": "Data Source", "DisplayName": "Append|OverWrite Data", "Order": 88, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/ExcelData/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Incremental Update", "Order": 89, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/IncrementalUpdateCOT/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Refresh COT", "Order": 90, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/RefreshCOT/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Streaming Dataset Endpoints", "Order": 91, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/StreamingDatasetInsertDataDialog/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Push Dataset Endpoints", "Order": 92, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/PushDataset/localization/en.ts"}, {"Module": "Dataset", "DisplayName": "Dataset Export", "Order": 93, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/Export/localization/en.ts"}, {"Module": "Data Model", "DisplayName": "Semantic Model 1", "Order": 94, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/SemanticDesigner/localization/en.ts"}, {"Module": "Data Model", "DisplayName": "Semantic Model 2", "Order": 95, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/SemanticDesigner/localization/dataset.en.ts"}, {"Module": "Data Model", "DisplayName": "Semantic Model 3", "Order": 96, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/SemanticDesigner/localization/portalData.en.ts"}, {"Module": "Dataset", "DisplayName": "Function Description", "Order": 97, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/Resource/FnDescription-en.json"}, {"Module": "Dataset", "DisplayName": "SQL Function Description", "Order": 98, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/Resource/SqlFunction-en.json"}, {"Module": "Dataset", "DisplayName": "Data Plugin Tile", "Order": 99, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/TilesRegister/localization/en.ts"}, {"Module": "Account", "DisplayName": "Account P<PERSON><PERSON>", "Order": 100, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/localization/portalLocaleData/en.js"}, {"Module": "Account", "DisplayName": "Account Plugin Basic info 1", "Order": 101, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/localization/en.js"}, {"Module": "Account", "DisplayName": "Account Plugin Basic info 2", "Order": 102, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/Common/localization/en.json"}, {"Module": "Account", "DisplayName": "Role Management", "Order": 103, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/RoleManagement/localization/en.json"}, {"Module": "Account", "DisplayName": "Tenant Management", "Order": 104, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/TenantManagement/localization/en.ts"}, {"Module": "Account", "DisplayName": "User Management", "Order": 105, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/UserManagement/localization/en.ts"}, {"Module": "IoT", "DisplayName": "IoT Designer", "Order": 106, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/IoTDataDesigner/localization/en.json"}, {"Module": "Account", "DisplayName": "Sync Settings", "Order": 107, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/SynchronizationSetting/localization/en.json"}, {"Module": "Data Model", "DisplayName": "Data Model Designer <PERSON><PERSON><PERSON>", "Order": 108, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/localization/error.en.json"}, {"Module": "Data Model", "DisplayName": "Data Model Designer", "Order": 109, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/localization/model.en.json"}, {"Module": "Data Model", "DisplayName": "Data Model Designer2", "Order": 110, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/localization/wizard.en.json"}, {"Module": "Data Model", "DisplayName": "Data Model Basic Info", "Order": 111, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/Register/localization/portal.en.ts"}, {"Module": "Data Model", "DisplayName": "Data Model Message Box", "Order": 112, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/Common/MessageBox/localization/en.ts"}, {"Module": "Data Model", "DisplayName": "Tasks", "Order": 113, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/TaskTab/localization/en.ts"}, {"Module": "Data Model", "DisplayName": "Reload Data", "Order": 114, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/PortalReload/localization/en.json"}, {"Module": "Data Model", "DisplayName": "Data Model Document List", "Order": 115, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/PortalReload/localization/portalLocaleData/en.ts"}, {"Module": "Data Model", "DisplayName": "Data Model Cache Management", "Order": 116, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/DataModelCacheManagement/localization/en.ts"}, {"Module": "Data Model", "DisplayName": "<PERSON><PERSON>", "Order": 117, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/Common/localization/apiError/en.json"}, {"Module": "Dashboard", "DisplayName": "Dashboard Setting", "Order": 118, "Obsolete": false, "Path": "gces-dashboard/frontend/src/adminPortal/localization/en.json"}, {"Module": "Dashboard", "DisplayName": "Portal 1", "Order": 119, "Obsolete": false, "Path": "gces-dashboard/frontend/src/portalRegister/localization/portal/en.json"}, {"Module": "Dashboard", "DisplayName": "Portal 2", "Order": 120, "Obsolete": false, "Path": "gces-dashboard/frontend/src/portalRegister/localization/dashboard/en.json"}, {"Module": "Dashboard", "DisplayName": "Portal 3", "Order": 121, "Obsolete": false, "Path": "gces-dashboard/frontend/src/portalRegister/localization/scene/en.json"}, {"Module": "Dashboard", "DisplayName": "3D Scene", "Order": 122, "Obsolete": false, "Path": "gces-dashboard/frontend/packages/sceneCore/locales/scene/en.json"}, {"Module": "Dashboard", "DisplayName": "Dashboard", "Order": 123, "Obsolete": false, "Path": "gces-dashboard/frontend/packages/dashboardCore/locales/dashboard/en.json"}, {"Module": "Dashboard", "DisplayName": "Dashboard Dependencies", "Order": 124, "Obsolete": false, "Path": "gces-dashboard/frontend/packages/dashboardCore/locales/third/en.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Common", "Order": 125, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/localization/en/common.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Notifications", "Order": 126, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/localization/en/reporting.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Settings Labels", "Order": 127, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/localization/en/portal.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Common - Portals", "Order": 128, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/common/localization/en/portal.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Features - Portals", "Order": 129, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/common/localization/en/reporting.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Export Templates", "Order": 130, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/ExportSettingTemplates/localization/en.json"}, {"Module": "Reporting", "DisplayName": "Reporting - User Functions", "Order": 131, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/UserFunctions/localization/en.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Settings", "Order": 132, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/ReportingSettings/localization/en.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Enums - Portals", "Order": 133, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/common/localization/en/enums.json"}, {"Module": "Report Designer", "DisplayName": "Designer - <PERSON>", "Order": 134, "Obsolete": false, "Path": "gces-reporting/gces-reporting-designer/src/i18n/wynReportDesigner/en.json"}, {"Module": "Report Designer", "DisplayName": "Designer - Dataset Validation", "Order": 135, "Obsolete": false, "Path": "gces-reporting/gces-reporting-designer/src/i18n/wynReportDesignerDataSetSchemaErrors/en.json"}, {"Module": "Report Designer", "DisplayName": "Core - Adorners", "Order": 136, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-adorners.json"}, {"Module": "Report Designer", "DisplayName": "Core - Captions", "Order": 137, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-captions.json"}, {"Module": "Report Designer", "DisplayName": "Core - Chart Wizard", "Order": 138, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-chartWizard.json"}, {"Module": "Report Designer", "DisplayName": "Core - Common", "Order": 139, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-common.json"}, {"Module": "Report Designer", "DisplayName": "Core - Components", "Order": 140, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-components.json"}, {"Module": "Report Designer", "DisplayName": "Core - Context Actions", "Order": 141, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-contextActions.json"}, {"Module": "Report Designer", "DisplayName": "Core - Defaults", "Order": 142, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-defaults.json"}, {"Module": "Report Designer", "DisplayName": "Core - Dialogs", "Order": 143, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-dialogs.json"}, {"Module": "Report Designer", "DisplayName": "Core - Documents API", "Order": 144, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-documentsAPI.json"}, {"Module": "Report Designer", "DisplayName": "Core - Enums", "Order": 145, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-enums.json"}, {"Module": "Report Designer", "DisplayName": "Core - Notifications 1", "Order": 146, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-error.json"}, {"Module": "Report Designer", "DisplayName": "Core - Expression Fields", "Order": 147, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-expressionFields.json"}, {"Module": "Report Designer", "DisplayName": "Core - Filters", "Order": 148, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-filters.json"}, {"Module": "Report Designer", "DisplayName": "Core - Group Editor", "Order": 149, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-groupEditor.json"}, {"Module": "Report Designer", "DisplayName": "Core - Labels", "Order": 150, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-labels.json"}, {"Module": "Report Designer", "DisplayName": "Core - <PERSON><PERSON>", "Order": 151, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-marginsSizes.json"}, {"Module": "Report Designer", "DisplayName": "Core - Name Templates", "Order": 152, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-nameTemplates.json"}, {"Module": "Report Designer", "DisplayName": "Core - Notifications 2", "Order": 153, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-notifications.json"}, {"Module": "Report Designer", "DisplayName": "Core - Page Sizes", "Order": 154, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-pageSizes.json"}, {"Module": "Report Designer", "DisplayName": "Core - Parameters View Editor", "Order": 155, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-parametersViewEditor.json"}, {"Module": "Report Designer", "DisplayName": "Core - Property Descriptors", "Order": 156, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-propertyDescriptors.json"}, {"Module": "Report Designer", "DisplayName": "Core - Property Editors", "Order": 157, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-propertyEditors.json"}, {"Module": "Report Designer", "DisplayName": "Core - Report Items", "Order": 158, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-reportItems.json"}, {"Module": "Report Designer", "DisplayName": "Core - Report Items Labels", "Order": 159, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-romLabels.json"}, {"Module": "Report Designer", "DisplayName": "Core - Tablix Wizard", "Order": 160, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-tablixWizard.json"}, {"Module": "Report Designer", "DisplayName": "Core - Validation", "Order": 161, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-validationErrors.json"}, {"Module": "Report Designer", "DisplayName": "Core - Notifications 3", "Order": 162, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/en/en-warning.json"}, {"Module": "Report Viewer", "DisplayName": "Viewer - Common", "Order": 163, "Obsolete": false, "Path": "gces-reporting/gces-reporting-viewer/src/i18n/en/wynReportViewer.json"}, {"Module": "Report Viewer", "DisplayName": "Viewer - Core", "Order": 164, "Obsolete": false, "Path": "gces-reporting/gces-reporting-viewer/src/i18n/en/viewer.json"}, {"Module": "Report Viewer", "DisplayName": "Viewer - Notifications", "Order": 165, "Obsolete": false, "Path": "gces-reporting/gces-reporting-viewer/src/i18n/en/wynReportViewerErrors.json"}, {"Module": "Reporting Components", "DisplayName": "Parameter Panel - Common", "Order": 166, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/en/params.json"}, {"Module": "Reporting Components", "DisplayName": "Parameter Panel - Validation", "Order": 167, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/en/paramsValidation.json"}, {"Module": "Reporting Components", "DisplayName": "Parameter Panel - View", "Order": 168, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/en/paramsView.json"}, {"Module": "Reporting Components", "DisplayName": "Parameters", "Order": 169, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/en/wynReportParameters.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Enums", "Order": 170, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/enums.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Common", "Order": 171, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/common.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Delivery", "Order": 172, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/delivery.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - CSV", "Order": 173, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/export-csv.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Word", "Order": 174, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/export-docx.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Excel", "Order": 175, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/export-excel.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Excel Data", "Order": 176, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/export-excel-data.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - HTML", "Order": 177, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/export-html.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Image", "Order": 178, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/export-image.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - JSON", "Order": 179, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/export-json.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - PDF", "Order": 180, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/export-pdf.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - TXT", "Order": 181, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/export-txt.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - XML", "Order": 182, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/en/export-xml.json"}, {"Module": "Reporting Utils", "DisplayName": "Http Client", "Order": 183, "Obsolete": false, "Path": "gces-reporting/gces-reporting-utils/src/httpClient/i18n/en.json"}, {"Module": "Reporting Utils", "DisplayName": "Report Languages", "Order": 184, "Obsolete": false, "Path": "gces-reporting/gces-reporting-utils/src/reportLanguages/i18n/en.json"}, {"Module": "Reporting Utils", "DisplayName": "Custom Parameters View", "Order": 185, "Obsolete": false, "Path": "gces-reporting/gces-reporting-utils/src/parametersView/i18n/en.json"}, {"Module": "Component Library", "DisplayName": "Document Reference", "Order": 186, "Obsolete": false, "Path": "gces-js-common/packages/gces-references-component/src/localization/en.ts"}, {"Module": "Component Library", "DisplayName": "Share", "Order": 187, "Obsolete": false, "Path": "gces-js-common/packages/wyn-components/src/localization/en.ts"}, {"Module": "ETL", "DisplayName": "ETL Designer", "Order": 188, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Add Formulas", "Order": 189, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/AddFormula/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Combine Records", "Order": 190, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/AppendRecords/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Combine Fields", "Order": 191, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/CombineFields/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Filter Records", "Order": 192, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/FilterRecords/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Group By", "Order": 193, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/GroupBy/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Input Source", "Order": 194, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/InputSource/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Join <PERSON>", "Order": 195, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/JoinData/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Output Target", "Order": 196, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/OutputTarget/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Transpose Row to Col", "Order": 197, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/Pivot/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Re<PERSON>ve Duplicates", "Order": 198, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/RemoveDuplicates/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Select <PERSON>", "Order": 199, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/SelectFields/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Set Field Type", "Order": 200, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/SetFieldType/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Split Records", "Order": 201, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/SplitRecords/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Transpose Col to Row", "Order": 202, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/Unpivot/localization/en.json"}, {"Module": "ETL", "DisplayName": "Command-Common Localization", "Order": 203, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/CommonLocalization/en.json"}, {"Module": "Dashboard", "DisplayName": "Chat Analysis", "Order": 204, "Obsolete": false, "Path": "gces-dashboard/frontend/src/smartAnalyzer/i18n/locals/en.json"}, {"Module": "Dashboard", "DisplayName": "Admin Portal", "Order": 205, "Obsolete": false, "Path": "gces-dashboard/frontend/src/adminPortal/common/localization/en.json"}, {"Module": "Reporting Components", "DisplayName": "Doc Permissions Tree", "Order": 206, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/PermissionsTree/i18n/en/permissionsTree.json"}, {"Module": "Admin Portal", "DisplayName": "Printer Management", "Order": 207, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/Printer/localization/en.json"}, {"Module": "Data Model", "DisplayName": "Data Model Designer <PERSON><PERSON><PERSON>", "Order": 208, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/localization/error.en.json"}, {"Module": "Data Model", "DisplayName": "Data Model Designer", "Order": 209, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/localization/model.en.json"}, {"Module": "Data Model", "DisplayName": "Data Model Designer2", "Order": 210, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/localization/wizard.en.json"}, {"Module": "Data Model", "DisplayName": "Data Model Cache Management", "Order": 211, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/CacheManagement/localization/en.ts"}, {"Module": "Data Model", "DisplayName": "<PERSON><PERSON>", "Order": 212, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/Common/localization/apiError/en.json"}, {"Module": "Data Model", "DisplayName": "Data Model Preview", "Order": 213, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisPreview/localization/en.json"}, {"Module": "Dataset", "DisplayName": "AI Settings", "Order": 214, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/common/AISettings/en.json"}]