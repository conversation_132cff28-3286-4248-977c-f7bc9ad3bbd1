#!/bin/bash
# $1 - wyn version
# $2 - source yum repository url
# $3 - source yum repository sas
# $4 - target yum repository url
# $5 - target yum repository sas

WynVersion=9.0
RpmFileName=wyn-enterprise-$1-main.x86_64.rpm

#SourceRepoURL="https://wynintdelivery.blob.core.windows.net/wynintartifacts/installers/$WynVersion/$SpecificVersion"
#TargetRepoUrl="https://wyndelivery.blob.core.windows.net/wynartifacts/BI/beta/yum/$WynVersion"
SourceRepoURL=$2
SourceRepoSAS=$3
TargetRepoURL=$4
TargetRepoSAS=$5

# check parameter
echo "check parameter"
if [[ "$1" != "$WynVersion.00"* ]]; then
  echo "invalid version number $1 detected, the version number should be $WynVersion.00xxx.0"
  exit 1
fi

# sync remote repo to local
echo "sync remote repo to local"
rm -rf ./repo > /dev/null 2>&1
mkdir repo
azcopy sync "$TargetRepoURL?$TargetRepoSAS" ./repo/ --recursive=true
if [ ! -d ./repo/repodata ]; then
  echo "failed to sync remote repo to local"
  exit 1
fi

# copy RPM file to local
echo "copy dev RPM file to local"
azcopy copy "$SourceRepoURL/$RpmFileName?$SourceRepoSAS" ./repo/
if [ ! -f ./repo/$RpmFileName ]; then
  echo "failed to copy dev RPM file to local"
  exit 1
fi

# create repo
echo "create repo"
cd ./repo
createrepo ./
cd ..

# sync local repo to azure storage
echo "push to azure storage"
azcopy sync "./repo" "$TargetRepoURL?$TargetRepoSAS" --recursive=true --delete-destination=true

# finish
echo "finished"
exit 0
