pipeline {
    agent {label 'vs2017'}
    environment {
	    build_version='v3.1'
        build_branch='release/${env.build_version}'
        wyn_local_path='d:\\WynFiles\\${env.build_version}'
		wyn_artifacts_path='D:\\WynArtifacts\\${env.build_version}'
    }	
    stages {
		stage('download artifacts'){
            steps{
				dir('gcesbuild'){
				    git branch:'${env.build_branch}', credentialsId:'c6790709-fee4-4ebe-8639-9c4c0486b701', url:' https://<EMAIL>/grapecity/gces-build.git'
				    bat 'del "${env.wyn_local_path}\\*.zip"'
				    bat '''
					    dotnet .\\tools\\Downloader\\gces-artifacts-downloader.dll .\\Release\\manifest.txt ${env.wyn_local_path}
						IF NOT %ERRORLEVEL% == 0 exit /b %ERRORLEVEL%
						'''
				}
			}
		}
        stage('pull code'){
            steps{
                git branch: 'master',
                credentialsId: 'd1319d0a-f036-414e-94d0-f61e71f245e1',
                url: 'http://xa-cm-tfs2:8080/tfs/CMCollection/_git/WynSetup'
				bat '''
					dotnet .\\PrepareFiles.dll ${env.wyn_local_path} .\\InstallImage\\Files
					IF NOT %ERRORLEVEL% == 0 exit /b %ERRORLEVEL%
					'''
				stash name:'origin-id-server', includes: 'InstallImage/Files/IdentityServer/**/*'
				stash name:'origin-server', includes: 'InstallImage/Files/Server/**/*'
				stash name:'obfuscation-config', includes: '*.xml'
            }
        }
		
		stage('obfuscation'){
			agent {label 'dotfuscator'}
            steps{
				unstash name:'origin-id-server'
				unstash name:'origin-server'
				unstash name:'obfuscation-config'
				bat '''
					"c:\\Program Files (x86)\\PreEmptive Solutions\\Dotfuscator Professional Edition\\dotfuscator.exe" obfuscate_server.xml /p=projectdir=.\\InstallImage\\Files\\Server
					"c:\\Program Files (x86)\\PreEmptive Solutions\\Dotfuscator Professional Edition\\dotfuscator.exe" obfuscate_identity.xml /p=projectdir=.\\InstallImage\\Files\\IdentityServer
					'''
				stash name:'obfuscated-id-server', includes: 'InstallImage/Files/IdentityServer/**/*'
				stash name:'obfuscated-server', includes: 'InstallImage/Files/Server/**/*'	
            }
        }

		stage('increase version'){
            steps{
                dir('.'){
					unstash name:'obfuscated-id-server'
					unstash name:'obfuscated-server'
                    powershell(". '.\\increaseversion.ps1' .")    
                }
            }
        }

		stage('push version changes'){
            steps{
			    bat """
			        set /p wynbuildversion=<%temp%\\wynbuildversion.txt
			        git config  user.name "SpreadBuilder"
                    git config  user.email <EMAIL>
			        git config  credential.helper wincred
				    git add ./Setup/PreProcessVar.wxi
					git add ./GrapeCityInstaller/PreProcessVar.wxi
                    git commit -m "CI new version %wynbuildversion%"
                    git push origin master
				"""
				
				dir('gcesbuild'){
					bat """
						set /p wynbuildversion=<%temp%\\wynbuildversion.txt
						git config --local user.name glovergao
						git config --local user.email <EMAIL>
						git config --local credential.helper wincred
						git tag %wynbuildversion%
                        git push --tags origin develop
					"""
				}
            }
        }
        stage('clean build'){
            steps{
				dir('.\\GrapeCityInstaller\\bin'){
					bat "del *.exe /S /F"
				}
            }
        }        
        stage('build'){
            steps{
                  bat '"C:\\Program Files (x86)\\Microsoft Visual Studio\\2017\\Enterprise\\MSBuild\\15.0\\Bin\\msbuild.exe" build-setup-x64-cn.xml'
            }
        }

        stage('linux installer') {
            steps {
                powershell '''
					$WynFiles = "${env.wyn_local_path}"
                    $LinuxInstalerPath = "./LinuxInstaller"
					$wynbuildversion = Get-Content $env:temp/wynbuildversion.txt
                    rm -Force -Recurse -ErrorAction Ignore -Path $LinuxInstalerPath
                    mkdir $LinuxInstalerPath
                    Expand-Archive -Path $WynFiles/identity-service.*.zip -DestinationPath $LinuxInstalerPath/IdentityServer
                    Expand-Archive -Path $WynFiles/server.*.zip -DestinationPath $LinuxInstalerPath/Server
                    Expand-Archive -Path $WynFiles/portal.*.zip -DestinationPath $LinuxInstalerPath/Portal
                    Expand-Archive -Path $WynFiles/monitoring-netcore.*.zip -DestinationPath $LinuxInstalerPath/Monitor
                    Expand-Archive -Path $WynFiles/gcef-data-providers.*.zip -DestinationPath $LinuxInstalerPath/DataProviders
                    Expand-Archive -Path $WynFiles/dataset-and-account.*.zip -DestinationPath $LinuxInstalerPath/Plugins
                    Expand-Archive -Path $WynFiles/dashboard.*.zip -DestinationPath $LinuxInstalerPath/Plugins
                    Expand-Archive -Path $WynFiles/reporting.*.zip -DestinationPath $LinuxInstalerPath/Plugins
                    mv -Path $LinuxInstalerPath/Plugins/plugin -Destination $LinuxInstalerPath/Plugins/reporting
                    mv -Path $LinuxInstalerPath/Plugins/worker -Destination $LinuxInstalerPath/ReportingWorker
					
                    cd $LinuxInstalerPath
					rm -Force -ErrorAction Ignore -Path ./wyn.tar.gz
                    tar zcf ./wyn.tar.gz ./IdentityServer/* ./Server/* ./Portal/* ./Monitor/* ./DataProviders/* ./Plugins/* ./ReportingWorker/*
					
                    rm -Force -ErrorAction Ignore -Recurse dist
                    mkdir dist
                    
                    rm -Force -ErrorAction Ignore ./aspnetcore-runtime-2.1.0-linux-x64.tar.gz
                    cp $WynFiles/installation-packages/aspnetcore-runtime-2.1.0-linux-x64.tar.gz ./
                    rm -Force -ErrorAction Ignore ./postgresql-10.4-1-linux-x64-binaries.tar.gz
                    cp $WynFiles/installation-packages/postgresql-10.4-1-linux-x64-binaries.tar.gz ./
                    rm -Force -ErrorAction Ignore ./liberation-fonts-ttf-2.00.1.tar.gz
                    cp $WynFiles/installation-packages/liberation-fonts-ttf-2.00.1.tar.gz ./
                    mkdir -Force ./dumps
                    cp ../CustomActions/*.dump ./dumps/

                    rm -Force -ErrorAction Ignore ./wyn-install.sh
                    cp ../gcesbuild/Linux/wyn-installer-ubuntu1404.sh ./wyn-install.sh
					cp -Force ../gcesbuild/Linux/uninstall.sh ./uninstall.sh
                    rm -Force -ErrorAction Ignore -Recurse ./dotnet-dependencies
					mkdir -Force ./dotnet-dependencies
                    cp -Force -Recurse $WynFiles/installation-packages/dotnet-deps/Ubuntu1404/* ./dotnet-dependencies
                    tar zcf dist/wynenterprise-ubuntu1404-$wynbuildversion.tar.gz ./dotnet-dependencies ./dumps ./aspnetcore-runtime-2.1.0-linux-x64.tar.gz ./postgresql-10.4-1-linux-x64-binaries.tar.gz ./liberation-fonts-ttf-2.00.1.tar.gz ./wyn.tar.gz ./wyn-install.sh ./uninstall.sh
                    
                    rm -Force -ErrorAction Ignore ./wyn-install.sh
                    cp ../gcesbuild/Linux/wyn-installer-ubuntu1604.sh ./wyn-install.sh
                    rm -Force -ErrorAction Ignore -Recurse ./dotnet-dependencies
					mkdir -Force ./dotnet-dependencies
                    cp -Force $WynFiles/installation-packages/dotnet-deps/Ubuntu1604/* ./dotnet-dependencies
                    tar zcf dist/wynenterprise-ubuntu1604-$wynbuildversion.tar.gz ./dotnet-dependencies ./dumps ./aspnetcore-runtime-2.1.0-linux-x64.tar.gz ./postgresql-10.4-1-linux-x64-binaries.tar.gz ./liberation-fonts-ttf-2.00.1.tar.gz ./wyn.tar.gz ./wyn-install.sh ./uninstall.sh
                    
                    rm -Force -ErrorAction Ignore ./wyn-install.sh
                    cp ../gcesbuild/Linux/wyn-installer-ubuntu1710.sh ./wyn-install.sh
                    rm -Force -ErrorAction Ignore -Recurse ./dotnet-dependencies
					mkdir -Force ./dotnet-dependencies
                    cp -Force $WynFiles/installation-packages/dotnet-deps/Ubuntu1710/* ./dotnet-dependencies
                    tar zcf dist/wynenterprise-ubuntu1710-$wynbuildversion.tar.gz ./dotnet-dependencies ./dumps ./aspnetcore-runtime-2.1.0-linux-x64.tar.gz ./postgresql-10.4-1-linux-x64-binaries.tar.gz ./liberation-fonts-ttf-2.00.1.tar.gz ./wyn.tar.gz ./wyn-install.sh ./uninstall.sh
                    
                    rm -Force -ErrorAction Ignore ./wyn-install.sh
                    cp ../gcesbuild/Linux/wyn-installer-ubuntu1804.sh ./wyn-install.sh
                    rm -Force -ErrorAction Ignore -Recurse ./dotnet-dependencies
					mkdir -Force ./dotnet-dependencies
                    cp -Force $WynFiles/installation-packages/dotnet-deps/Ubuntu1804/* ./dotnet-dependencies
                    tar zcf dist/wynenterprise-ubuntu1804-$wynbuildversion.tar.gz ./dotnet-dependencies ./dumps ./aspnetcore-runtime-2.1.0-linux-x64.tar.gz ./postgresql-10.4-1-linux-x64-binaries.tar.gz ./liberation-fonts-ttf-2.00.1.tar.gz ./wyn.tar.gz ./wyn-install.sh ./uninstall.sh
                    
                    rm -Force -ErrorAction Ignore ./wyn-install.sh
                    cp ../gcesbuild/Linux/wyn-installer-centos.sh ./wyn-install.sh
                    rm -Force -ErrorAction Ignore -Recurse ./dotnet-dependencies
					mkdir -Force ./dotnet-dependencies
                    cp -Force $WynFiles/installation-packages/dotnet-deps/CentOS/* ./dotnet-dependencies
                    tar zcf dist/wynenterprise-centos-$wynbuildversion.tar.gz ./dotnet-dependencies ./dumps ./aspnetcore-runtime-2.1.0-linux-x64.tar.gz ./postgresql-10.4-1-linux-x64-binaries.tar.gz ./liberation-fonts-ttf-2.00.1.tar.gz ./wyn.tar.gz ./wyn-install.sh ./uninstall.sh
                    
                    rm -Force -ErrorAction Ignore ./wyn-install.sh
                    cp ../gcesbuild/Linux/wyn-installer-rhel.sh ./wyn-install.sh
                    rm -Force -ErrorAction Ignore -Recurse ./dotnet-dependencies
					mkdir -Force ./dotnet-dependencies
                    cp -Force $WynFiles/installation-packages/dotnet-deps/RHEL/* ./dotnet-dependencies
                    tar zcf dist/wynenterprise-rhel-$wynbuildversion.tar.gz ./dotnet-dependencies ./dumps ./aspnetcore-runtime-2.1.0-linux-x64.tar.gz ./postgresql-10.4-1-linux-x64-binaries.tar.gz ./liberation-fonts-ttf-2.00.1.tar.gz ./wyn.tar.gz ./wyn-install.sh ./uninstall.sh
                '''
            }
        }
        stage('save artifacts'){
            steps{
                  bat """
                     set /p wynbuildversion=<%temp%\\wynbuildversion.txt
                     copy .\\GrapeCityInstaller\\bin\\x64\\Release-cn\\*%wynbuildversion%.exe  ${env.wyn_artifacts_path}\\windows\\ /Y
					 mkdir ${env.wyn_artifacts_path}\\linux\\%wynbuildversion%
					 xcopy .\\LinuxInstaller\\dist\\*%wynbuildversion%.tar.gz  ${env.wyn_artifacts_path}\\linux\\%wynbuildversion%\\ /Y
                  """
            }
        } 	
        stage('upload artifacts to azure'){
            steps{
				script {
				    lastestversion = readFile("${temp}/wynbuildversion.txt").trim()
				}
				dir('GrapeCityInstaller/bin/x64/Release-cn/'){
					azureUpload blobProperties: [cacheControl: '', contentEncoding: '', contentLanguage: '', contentType: '', detectContentType: true], containerName: 'artifacts', fileShareName: '', filesPath: '*.exe', virtualPath: "installer/CN/windows/", storageCredentialId: 'fc3f14ec-5d3e-42a8-89b0-504cda375f59', storageType: 'blobstorage', uploadArtifactsOnlyIfSuccessful: true	
				}
				dir('LinuxInstaller/dist/'){
					azureUpload blobProperties: [cacheControl: '', contentEncoding: '', contentLanguage: '', contentType: '', detectContentType: true], containerName: 'artifacts', fileShareName: '', filesPath: '*.tar.gz', virtualPath: "installer/CN/linux/${lastestversion}/", storageCredentialId: 'fc3f14ec-5d3e-42a8-89b0-504cda375f59', storageType: 'blobstorage', uploadArtifactsOnlyIfSuccessful: true	
				}
            }
        }		
    }

    post {
        always {
            step([$class: 'Mailer',
                notifyEveryUnstableBuild: true,
                recipients: "<EMAIL>",
                sendToIndividuals: true])
        }
    }
}