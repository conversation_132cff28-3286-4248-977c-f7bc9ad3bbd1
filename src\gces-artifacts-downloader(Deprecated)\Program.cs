﻿using System;
using System.IO;
using System.Linq;

namespace gces_artifacts_downloader
{
	static class Program
	{
		static int Main(string[] args)
		{
			if (args.Length < 2)
			{
				Console.WriteLine("Usage: dotnet gces-artifacts-downloader.dll {manifest_file_location} {output_dir} [{auto_detect_version} {version_detect_file_location}]");
				Environment.Exit(-1);
			}

			var manifestFilePath = args[0];
			var outputDir = args[1];
			if (args.Length > 3)
			{
				var autoDetectVersion = args[2];
				if (bool.TryParse(autoDetectVersion, out bool autoDetect) && autoDetect)
				{
					var versionDetectFilePath = args[3];
					var versionDetector = new VersionDetector(versionDetectFilePath);
					var latestVersions = versionDetector.DetectTheLatestVersionsAsync().Result;
					if (null != latestVersions)
					{
						var lines = latestVersions.Select(v => string.Format("{0,-30} {1}", v.repoName, v.artifactName)).ToArray();
						File.WriteAllLines(manifestFilePath, lines);
					}
				}
			}

			var downloader = new ArtifactsDownloader(manifestFilePath, outputDir);
			var tups = downloader.GetNeedDownloadedArtifacts();
			if (null != tups && tups.Any() && !downloader.DownloadArtifacts(tups))
			{
				return 1;
			}
			return 0;
		}
	}
}