=========================== POSTGRESQL ==========================
https://dev.monetdb.org/hg/MonetDB/file/tip/documentation/source/build-debian.rst
sudo apt-get udpate
sudo apt-get install -y postgresql
sudo groupadd --system wyn-enterprise
sudo useradd --system wyn-enterprise
sudo mkdir -p /opt/postgresql/data
sudo chown -R wyn-enterprise:wyn-enterprise /opt/postgresql/data
sudo chown -R wyn-enterprise:wyn-enterprise /var/run/postgresql
cd /opt/postgresql
sudo echo "Wr8TGfe2r0" > .pwf
sudo -u wyn-enterprise /lib/postgresql/11/bin/initdb -D /opt/postgresql/data -A md5 -E utf8 --pwfile=.pwf
sudo -u wyn-enterprise /lib/postgresql/11/bin/pg_ctl -o "-p 5444" -D /opt/postgresql/data -l /opt/postgresql/data/logfile start
sudo -u wyn-enterprise /lib/postgresql/11/bin/pg_ctl -o "-p 5444" -D /opt/postgresql/data stop
sudo rm .pwf
-- edit /lib/systemd/system/postgresql.service --
sudo systemctl enable postgresql
sudo systemctl daemon-reload
sudo systemctl start postgresql

=========================== MONETDB ==========================
#sudo groupadd --system monetdb
#sudo useradd --system monetdb

sudo rm -rf /opt/monetdb/var/monetdb5/dbfarm
sudo mkdir -p /opt/monetdb/var/monetdb5/dbfarm
sudo echo "user=monetdb
password=monetdb
language=sql" > /opt/monetdb/var/monetdb5/dbfarm/.monetdb
sudo /opt/monetdb/bin/monetdbd create /opt/monetdb/var/monetdb5/dbfarm
sudo /opt/monetdb/bin/monetdbd set port=54321 /opt/monetdb/var/monetdb5/dbfarm
sudo /opt/monetdb/bin/monetdb create wyndw
sudo /opt/monetdb/bin/monetdb set nclients=200 wyndw
sudo /opt/monetdb/bin/monetdb release wyndw

-----------------------------------------------------------CENTOS7 ARM-----------------------------------------------------------
https://dev.monetdb.org/hg/MonetDB/file/tip/documentation/source/build-fedora.rst
sudo yum updateinfo
sudo yum install -y postgresql-server
sudo groupadd --system wyn-enterprise
sudo useradd --system wyn-enterprise -g wyn-enterprise
sudo mkdir -p /opt/postgresql/data
sudo chown -R wyn-enterprise:wyn-enterprise /opt/postgresql/data
sudo chown -R wyn-enterprise:wyn-enterprise /var/run/postgresql
cd /opt/postgresql
sudo echo "Wr8TGfe2r0" > .pwf
sudo -u wyn-enterprise /usr/bin/initdb -D /opt/postgresql/data -A md5 -E utf8 --pwfile=.pwf
sudo -u wyn-enterprise /usr/bin/pg_ctl -o "-p 5444" -D /opt/postgresql/data -l /opt/postgresql/data/logfile start
sudo -u wyn-enterprise /usr/bin/pg_ctl -o "-p 5444" -D /opt/postgresql/data stop
sudo rm .pwf
-- edit /lib/systemd/system/postgresql.service --
sudo systemctl enable postgresql
sudo systemctl daemon-reload
sudo systemctl start postgresql

=======================================================
sudo groupadd --system monetdb
sudo useradd --system monetdb -g monetdb
sudo rm -rf /opt/monetdb/var/monetdb5/dbfarm
sudo mkdir -p /opt/monetdb/var/monetdb5/dbfarm
sudo echo "user=monetdb
password=monetdb
language=sql" > /opt/monetdb/var/monetdb5/dbfarm/.monetdb
sudo chown -R monetdb:monetdb /opt/monetdb
sudo -u monetdb /opt/monetdb/bin/monetdbd create /opt/monetdb/var/monetdb5/dbfarm
sudo -u monetdb /opt/monetdb/bin/monetdbd set port=54321 /opt/monetdb/var/monetdb5/dbfarm
sudo -u monetdb /opt/monetdb/bin/monetdbd start /opt/monetdb/var/monetdb5/dbfarm
sudo -u monetdb /opt/monetdb/bin/monetdb create wyndw
sudo -u monetdb /opt/monetdb/bin/monetdb set nclients=200 wyndw
sudo -u monetdb /opt/monetdb/bin/monetdb release wyndw
