name: Build Installers

on:
  workflow_dispatch:

  # cron:
  #   - cron: "* 14 * * 1-5"

env:
  PRODUCT_VERSION: 9.0
  DEV_PLUGIN_ARTIFACTS_SAS_TOKEN: ${{ secrets.DEV_PLUGIN_ARTIFACTS_SAS_TOKEN }}
  DEV_ARTIFACTS_SAS_TOKEN: ${{ secrets.DEV_ARTIFACTS_SAS_TOKEN }}
  CDN_SAS_TOKEN: ${{ secrets.CDN_SAS_TOKEN }}
  AzureSignWYN: ${{ secrets.AZURE_SIGN_WYN }}
  DEV_ARTIFACTS_URL: ${{ variables.DEV_ARTIFACTS_URL }}
  DEV_DOCKER_REGISTRY: ${{ variables.DEV_DOCKER_REGISTRY }}
  DEV_DOCKER_USERNAME: ${{ secrets.DEV_DOCKER_USERNAME }}
  DEV_DOCKER_PASSWORD: ${{ secrets.DEV_DOCKER_PASSWORD }}
  ARTIFACTS_DIR: "/var/tmp/wyn-build/artifacts"
  CONTENT_DIR: "/var/tmp/wyn-build/installation_content"

jobs:
  get-build-version:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    outputs:
      build_version: ${{ steps.increase-build-version.outputs.build_version }}

    steps:
      - name: Checkout gces-build
        uses: actions/checkout@v4
        with:
          repository: wyn-core/gces-build
          ref: refs/heads/release/v${{ env.PRODUCT_VERSION }}-en
          path: gces-build

      - name: Increase build version
        id: increase-build-version
        run: |
          cd ./gces-build
          version=$(< Release/version.txt)
          IFS='.' read -r MAJOR MINOR BUILD PATCH <<< "$version"
          build_number=$((10#$BUILD + 1))
          build_fmt=$(printf "%05d" $build_number)
          build_version="$MAJOR.$MINOR.$build_fmt.$PATCH"
          echo "build_version=$build_version" >> $GITHUB_OUTPUT

  build-windows-installers:
    needs: get-build-version
    runs-on: windows
    defaults:
      run:
        shell: pwsh
    env:
      BUILD_VERSION: ${{ needs.get-build-version.outputs.build_version }}

    steps:
      - name: Checkout windows-installer
        uses: actions/checkout@v4
        with:
          repository: wyn-core/gces-installer-windows2
          ref: refs/heads/release/v${{ env.PRODUCT_VERSION }}-en
          token: ${{ secrets.GH_PAT }}
          path: windows-installer         

      - name: Checkout gces-build
        uses: actions/checkout@v4
        with:
          repository: wyn-core/gces-build
          ref: refs/heads/release/v${{ env.PRODUCT_VERSION }}-en
          path: gces-build

      - name: Download artifacts
        run: |
          Set-Location gces-build
          echo "Downloading artifacts..."
          del ..\WynFiles\* -Recurse -Force -ErrorAction SilentlyContinue
          dotnet .\tools\NewDownloader\ArtifactsDownloader.dll -m .\Release\manifest.txt -o ..\WynFiles -v .\Release\version-detect.txt -a -e Dependencies_EN_$env:PRODUCT_VERSION -r plugin_artifacts -c "https://wynintdelivery.blob.core.windows.net/wynintartifacts?$env:DEV_PLUGIN_ARTIFACTS_SAS_TOKEN"
          Pop-Location

      - name: Increase version
        run: |
          Set-Location windows-installer
          .\increaseversion.ps1 . $env:BUILD_VERSION
          Pop-Location

      - name: Build language packages
        run: |
          Set-Location gces-build
          echo "Building language packages..."
          dotnet .\tools\Bundler\gces-language-package-bundler.dll .\Release\manifest.txt ..\WynFiles $env:BUILD_VERSION True .\Release\resource-order
          #azcopy cp ..\WynFiles\language-package-*-EN.zip "$env:DEV_ARTIFACTS_URL/$env:PRODUCT_VERSION/$env:BUILD_VERSION/lang-pack/?$env:DEV_ARTIFACTS_SAS_TOKEN"
          Pop-Location

      - name: Prepare content
        run: |
          Set-Location windows-installer
          $pver = $env:PRODUCT_VERSION -replace '\.', '_'
          $filename = "wynbuildversion${pver}_en"
          dotnet .\PrepareFiles.dll ..\WynFiles .\InstallImage\Files $env:BUILD_VERSION $filename
          Pop-Location

      - name: Push gces-build tag
        run: |
          Set-Location gces-build
          Set-Content -Path .\Release\version.txt -NoNewLine -Value $env:BUILD_VERSION
          git config --global user.name "gitea-actions"
          git config --global user.email "<EMAIL>"
          $diff1 = git diff --name-only HEAD -- Release/manifest.txt
          if ($diff1) {
            git add Release/manifest.txt
          }
          $diff2 = git diff --name-only HEAD -- Release/resource-order/en-resource-order.json
          if ($diff2) {
            git add Release/resource-order/*
          }
          git add Release/version.txt
          git commit -m "upgrade version - gitea-actions"
          git push https://${{ secrets.GH_PAT }}@code.wynenterprise.io/wyn-core/gces-build.git
          git tag $env:BUILD_VERSION
          git push "https://${{ secrets.GH_PAT }}@code.wynenterprise.io/wyn-core/gces-build.git" $env:BUILD_VERSION
          Pop-Location

      - name: Push windows-installer tag
        run: |
          Set-Location windows-installer
          git config --global user.name "gitea-actions"
          git config --global user.email "<EMAIL>"
          git add ./Setup/PreProcessVar.wxi
          git add ./GrapeCityInstaller/PreProcessVar.wxi
          git add ./MSI/CotWorker/Variable.wxi
          git add ./MSI/ReportWorker/Variable.wxi
          git add ./MSI/Server/Variable.wxi
          git add ./MSI/Prepare/Variable.wxi
          git add ./MSI/Finish/Variable.wxi
          git add ./MSI/JavaMemoryService/Variable.wxi
          git add ./MSI/JavaDataSourceService/Variable.wxi
          git add ./MSI/DashboardWorker/Variable.wxi
          git add ./MSI/SchedulerService/Variable.wxi
          git commit -m "CI new version $env:BUILD_VERSION"
          git push "https://${{ secrets.GH_PAT }}@code.wynenterprise.io/wyn-core/gces-installer-windows2.git"
          git tag $env:BUILD_VERSION
          git push "https://${{ secrets.GH_PAT }}@code.wynenterprise.io/wyn-core/gces-installer-windows2.git" $env:BUILD_VERSION
          Pop-Location

      - name: Build custom action
        run: |
          Set-Location windows-installer
          dotnet .\RemoveUnusedRuntimesFile.dll
          "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BuildCustomAction.xml
          Pop-Location

      - name: Update file name
        run: |
          Set-Location windows-installer
          .\updatename.ps1
          Pop-Location

      - name: Build installer
        run: |
          Set-Location windows-installer
          "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BuildMsiCotWorker.xml
          "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BuildMsiFinish.xml
          "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BuildMsiPrepare.xml
          "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BuildMsiReportWorker.xml
          "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BuildMsiServer.xml
          "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BuildMsiSetup.xml
          "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BuildMsiJavaDataSourceService.xml
          "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" BuildMsiJavaMemoryService.xml
          MSBuild.exe BuildMsiDashboardWorker.xml
          MSBuild.exe BuildMsiSchedulerService.xml
          MSBuild.exe BuildInstaller.xml
          Pop-Location

      - name: Upload installer
        run: |
          Set-Location windows-installer
          echo "Uploading installer..."
          # azcopy cp "GrapeCityInstaller\\bin\\x64\\Release-Offline\\WynEnterprise-x64-$env:BUILD_VERSION.exe" "$env:DEV_ARTIFACTS_URL/$env:PRODUCT_VERSION/$env:BUILD_VERSION/WynEnterprise-x64-offline-$env:BUILD_VERSION.exe?$env:DEV_ARTIFACTS_SAS_TOKEN"
          Pop-Location

      # - name: Upload msi
      #   run: |
      #     Set-Location windows-installer
      #     echo "Uploading msi..."
      #     azcopy cp "InstallImage\\SetupMsi\\*.msi" "https://wyndelivery.blob.core.windows.net/wynartifacts/BI/installation/windows/online/msi?$env:CDN_SAS_TOKEN" --include-pattern "*.msi" --recursive
      #     azcopy cp "GrapeCityInstaller\\bin\\x64\\Release-Online\\WynEnterprise-x64-$env:BUILD_VERSION.exe" "$env:DEV_ARTIFACTS_URL/$env:PRODUCT_VERSION/$env:BUILD_VERSION/WynEnterprise-x64-online-$env:BUILD_VERSION.exe?$env:DEV_ARTIFACTS_SAS_TOKEN" --include-pattern "*.msi" --recursive
      #     Pop-Location

  build-linux-installers:
    needs: [get-build-version, build-windows-installers]
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    env:
      BUILD_VERSION: ${{ needs.get-build-version.outputs.build_version }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          repository: wyn-core/gces-build
          ref: refs/heads/release/v${{ env.PRODUCT_VERSION }}-en

      - name: Setup NetCore SDK
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: |
            6.0.x
            8.0.x
          cache: true

      - name: Setup zip
        run: |
          apt-get update
          apt-get install -y apt-utils
          apt-get install -y zip

      - name: Download artifacts
        run: |
          mkdir -p $ARTIFACTS_DIR
          echo "downloading artifacts..."
          dotnet ./Tools/NewDownloader/ArtifactsDownloader.dll -m ./Release/manifest.txt -o $ARTIFACTS_DIR -v ./Release/version-detect.tx -e Dependencies_EN_Linux_$PRODUCT_VERSION -r plugin_artifacts -c https://wynintdelivery.blob.core.windows.net/wynintartifacts?$DEV_PLUGIN_ARTIFACTS_SAS_TOKEN

      - name: Build language packs
        run: |
          echo "building language packs..."
          dotnet ./Tools/Bundler/gces-language-package-bundler.dll ./Release/manifest.txt $ARTIFACTS_DIR $BUILD_VERSION True ./Release/resource-order

      - name: Prepare content
        run: |
          echo "preparing content..."
          mkdir -p $CONTENT_DIR
          bash ./prepare_content.sh $ARTIFACTS_DIR $CONTENT_DIR $BUILD_VERSION

      - name: Setup Docker Buildx
        run: |
          docker buildx ls | grep linux/arm64 > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "docker buildx is already setupped"
          else
            echo "setting up docker buildx..."
            apt-get update
            apt-get install -y qemu-user-static binfmt-support
            docker run --rm --privileged multiarch/qemu-user-static --reset -p yes
            docker buildx ls | grep linux/arm64
            if [ "0" == "$?" ]; then
              echo "docker buildx is setupped successfully"
            else
              echo "setup docker buildx failed"
              exit 1
            fi
          fi

      - name: Build x64 docker image
        run: |
          echo "building x64 docker image..."
          IMAGE_NAME=wyn-$PRODUCT_VERSION-en-dev
          cd ./EN/Docker
          bash build-and-push.sh $IMAGE_NAME $BUILD_VERSION $CONTENT_DIR $ARTIFACTS_DIR/dependencies/builtin_data $DEV_DOCKER_REGISTRY $DEV_DOCKER_USERNAME $DEV_DOCKER_PASSWORD
          docker manifest inspect $DEV_DOCKER_REGISTRY/$IMAGE_NAME:$BUILD_VERSION > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "build and push x64 docker image successfully"
            exit 0
          else
            echo "build and push x64 docker image failed"
            exit 1
          fi

      - name: Build arm64 docker image
        run: |
          echo "building arm64 docker image..."
          IMAGE_NAME=wyn-$PRODUCT_VERSION-en-arm64-dev
          cd ./EN/Docker
          bash build-and-push.sh $IMAGE_NAME $BUILD_VERSION $CONTENT_DIR $ARTIFACTS_DIR/dependencies/builtin_data $DEV_DOCKER_REGISTRY $DEV_DOCKER_USERNAME $DEV_DOCKER_PASSWORD --platform linux/arm64
          docker manifest inspect $DEV_DOCKER_REGISTRY/$IMAGE_NAME:$BUILD_VERSION > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "build and push arm64 docker image successfully"
            exit 0
          else
            echo "build and push arm64 docker image failed"
            exit 1
          fi

      - name: Build k8s docker image
        run: |
          echo "building k8s docker image..."
          IMAGE_NAME=wyn-$PRODUCT_VERSION-en-k8s-dev
          cd ./EN/Docker
          bash build-and-push.sh $IMAGE_NAME $BUILD_VERSION $CONTENT_DIR $ARTIFACTS_DIR/dependencies/builtin_data $DEV_DOCKER_REGISTRY $DEV_DOCKER_USERNAME $DEV_DOCKER_PASSWORD --platform linux/amd64,linux/arm64 --manifest
          docker manifest inspect $DEV_DOCKER_REGISTRY/$IMAGE_NAME:$BUILD_VERSION > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "build and push k8s docker image successfully"
            exit 0
          else
            echo "build and push k8s docker image failed"
            exit 1
          fi

      - name: Setup azcopy
        run: |
          azcopy -v > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "azcopy is already installed"
          else
            echo "installing azcopy..."
            cd /var/tmp
            curl -sL https://aka.ms/downloadazcopy-v10-linux | tar -xz
            cp ./azcopy_linux_amd64_*/azcopy /usr/bin/
            cd -
          fi

      - name: Build deb package
        run: |
          echo "building deb package..."
          cd ./EN/Linux/apt
          bash ./build-dev.sh $BUILD_VERSION $ARTIFACTS_DIR/dependencies/builtin_data $CONTENT_DIR "$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$BUILD_VERSION" "$DEV_ARTIFACTS_SAS_TOKEN"

      - name: Setup rpm build environment
        run: |
          rpmbuild --version > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "rpmbuild is already installed"
          else
            echo "setting up rpm build environment..."
            apt-get update
            apt-get install -y rpm
            mkdir -p /var/rpmbuild/{BUILD,BUILDROOT,RPMS/x86_64,SOURCES,SPECS,SRPMS,tmp}
            echo "%_topdir /var/rpmbuild" > ~/.rpmmacros
            echo "%_tmppath %{_topdir}/tmp" >> ~/.rpmmacros
          fi

      - name: Build rpm package
        run: |
          echo "building rpm package..."
          cd ./EN/Linux/yum
          bash ./build-dev.sh $BUILD_VERSION $ARTIFACTS_DIR/dependencies/builtin_data $CONTENT_DIR "$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$BUILD_VERSION" "$DEV_ARTIFACTS_SAS_TOKEN"
