#!/bin/bash

WYN_DATASOURCE_SERVICE="wyn-datasource.service"
SERVICE_FILE_LOCATION="/lib/systemd/system"
if [ ! -d "$SERVICE_FILE_LOCATION" ]; then
  SERVICE_FILE_LOCATION="/usr/lib/systemd/system"
fi

if [ ! -f "$SERVICE_FILE_LOCATION/$WYN_DATASOURCE_SERVICE" ]; then
  echo "[Unit]
Description=Wyn Data Source Service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/Wyn/DataSourceService
ExecStart=/opt/Wyn/jre/bin/java -jar gcef-datasource-service.jar
Restart=always
RestartSec=15

[Install]
WantedBy=multi-user.target" > "$SERVICE_FILE_LOCATION/$WYN_DATASOURCE_SERVICE"
fi

sudo systemctl enable --now $WYN_DATASOURCE_SERVICE > /dev/null 2>&1
sudo systemctl daemon-reload > /dev/null 2>&1
echo "Install Wyn datasource service 'wyn-datasource' successfully."

exit 0
