#!/bin/bash
# $1 - wyn version
# $2 - denpendency folder
# $3 - content folder
# $4 - apt repository url
# $5 - apt repository sas

WynVersion=9.0
WorkspaceDir=workspace
DpkgDir=$WorkspaceDir/$1
WynDir=$DpkgDir/opt/Wyn
DebianDir=$DpkgDir/DEBIAN
DebFileName=wynenterprise-$1.deb

DependencyDir=$2
ContentDir=$3
# RepoURL="https://gcesartifacts.blob.core.windows.net/artifacts/deb_files/$WynVersion-dev"
RepoURL=$4
RepoSAS=$5

FontFile=$DependencyDir/liberation-fonts-ttf-2.00.1.tar.gz
MonetDBPath=$DependencyDir/monetdb-11.43.23-apt.tar.gz
JRE8File=$DependencyDir/jre8.tar.gz

GpgKeyPswd=kfhIoEufy08asG

# check parameter
if [[ "$1" != "$WynVersion.00"* ]]; then
  echo "invalid version number, the version number should be $WynVersion.00xxx.0"
  exit 1
fi

# extract artifact file to workspace
echo "extract artifact file to workspace"

rm -rf $WorkspaceDir > /dev/null 2>&1
mkdir -p $WynDir
cp -rf $ContentDir/* $WynDir
shopt -s globstar
rm -rf ./**/runtimes/browser*
rm -rf ./**/runtimes/macc*
rm -rf ./**/runtimes/android*
rm -rf ./**/runtimes/solaris*
rm -rf ./**/runtimes/tvos*
rm -rf ./**/runtimes/freebsd*
rm -rf ./**/runtimes/illumos*
rm -rf ./**/runtimes/ios*
rm -rf ./**/runtimes/osx*
rm -rf ./**/runtimes/alpine*
rm -rf ./**/runtimes/linux-mips*
rm -rf ./**/runtimes/linux-musl*
rm -rf ./**/runtimes/linux-s390*
rm -rf ./**/runtimes/linux-ppc64*
rm -rf ./**/runtimes/linux-arm*
rm -rf ./**/runtimes/win*
rm -rf $WynDir/ServiceRunner
cp ../configure.sh $WynDir/Monitor
mv $WynDir/Monitor/conf/Wyn.conf $WynDir/Monitor/conf/Wyn.conf.origin
cp -r ../services $WynDir/
cp $JRE8File $WynDir/
cp $FontFile $WynDir/
cp $MonetDBPath $WynDir/monetdb-11.43.23.tar.gz

# calc package size
TAB="	"
PACKAGE_SIZE=$(du -s $WynDir)
IDX=$(expr index "$PACKAGE_SIZE" "$TAB")
PACKAGE_SIZE=$(expr substr "$PACKAGE_SIZE" 1 $IDX)

# build DEB package file
echo "build DEB package file"
mkdir -p $DebianDir
echo "Package: wyn-enterprise
Version: $1
Maintainer: wynEnterprise
Architecture: all
Installed-Size: $PACKAGE_SIZE
Description: Wyn Enterprise
Depends: aspnetcore-runtime-8.0 (>=8.0.16), curl
" > $DebianDir/control
cp ./preinst $DebianDir
chmod +x $DebianDir/preinst
cp ./postinst $DebianDir
chmod +x $DebianDir/postinst
cp ./prerm $DebianDir
chmod +x $DebianDir/prerm
cp ./postrm $DebianDir
chmod +x $DebianDir/postrm

echo "build DEB package file"
cd $WorkspaceDir
dpkg-deb -b ../$DpkgDir
mv ./$1.deb ./$DebFileName

echo "push to azure storage"
azcopy copy $DebFileName "$RepoURL/$DebFileName?$RepoSAS"

cd -
rm -rf $WorkspaceDir > /dev/null 2>&1

echo "finished"
