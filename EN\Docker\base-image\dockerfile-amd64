FROM gcescr.azurecr.io/gces-base-amd64-8.0:latest

ENV DEBIAN_FRONTEND=noninteractive

RUN echo 'deb http://apt.postgresql.org/pub/repos/apt jammy-pgdg main' > /etc/apt/sources.list.d/pgdg.list && \
    wget --output-document=- https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add - && \
    apt-get update && \
    apt-get install -y postgresql-10 && \
    pg_dropcluster 10 main && \
    localedef -i en_US -f UTF-8 en_US.UTF-8