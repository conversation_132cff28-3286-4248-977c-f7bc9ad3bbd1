FROM gcescr.azurecr.io/gces-base-amd64-8.0

ENV DEBIAN_FRONTEND=noninteractive

RUN mkdir -p /var/monetdb5/dbfarm && \
    mkdir -p /usr/share/fonts/liberation-fonts-ttf-2.00.1 && \
    mkdir -p /usr/share/fonts/custom

ADD ./wyn ./app
COPY ./startup.sr.sh /startup.sh
COPY ./jre /app/jre
COPY ./liberation-fonts-ttf-2.00.1 /usr/share/fonts/liberation-fonts-ttf-2.00.1

WORKDIR /app/ServiceRunner

EXPOSE 5000

CMD ["/bin/bash", "/startup.sh"]