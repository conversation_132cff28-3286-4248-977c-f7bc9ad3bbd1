name: Build Windows Installers

on:
  workflow_call:
    inputs:
      product_version:
        description: 'The product version'
        required: true
        type: string
      build_version:
        description: 'The build version'
        required: true
        type: string
      dev_artifacts_url:
        required: true
        type: string
    secrets:
      token:
        required: true
        type: string
      dev_plugin_artifacts_sas_token:
        required: true
        type: string
      dev_artifacts_sas_token:
        required: true
        type: string
      cdn_sas_token:
        required: true
        type: string
      azure_sign_wyn:
        required: true
        type: string

env:
  DEV_PLUGIN_ARTIFACTS_SAS_TOKEN: ${{ secrets.dev_plugin_artifacts_sas_token }}
  DEV_ARTIFACTS_SAS_TOKEN: ${{ secrets.dev_artifacts_sas_token }}
  CDN_SAS_TOKEN: ${{ secrets.cdn_sas_token }}
  AzureSignWYN: ${{ secrets.azure_sign_wyn }}
  DEV_ARTIFACTS_URL: ${{ inputs.dev_artifacts_url }}
  PRODUCT_VERSION: ${{ inputs.product_version }}
  BUILD_VERSION: ${{ inputs.build_version }}

jobs:
  build-windows-installers:
    runs-on: windows
    defaults:
      run:
        shell: pwsh

    steps:
      - name: Checkout windows-installer
        uses: actions/checkout@v4
        with:
          repository: wyn-core/gces-installer-windows2
          ref: refs/heads/release/v${{ env.PRODUCT_VERSION }}-en
          token: ${{ secrets.token }}
          path: windows-installer         

      - name: Checkout gces-build
        uses: actions/checkout@v4
        with:
          repository: wyn-core/gces-build
          ref: refs/heads/release/v${{ env.PRODUCT_VERSION }}-en
          path: gces-build

      - name: Download artifacts
        run: |
          Set-Location gces-build
          echo "Downloading artifacts..."
          del ..\WynFiles\* -Recurse -Force -ErrorAction SilentlyContinue
          dotnet .\tools\NewDownloader\ArtifactsDownloader.dll -m .\Release\manifest.txt -o ..\WynFiles -v .\Release\version-detect.txt -a -e Dependencies_EN_$env:PRODUCT_VERSION -r plugin_artifacts -c "https://wynintdelivery.blob.core.windows.net/wynintartifacts?$env:DEV_PLUGIN_ARTIFACTS_SAS_TOKEN"
          Pop-Location

      - name: Increase version
        run: |
          Set-Location windows-installer
          .\increaseversion.ps1 . $env:BUILD_VERSION
          Pop-Location

      - name: Build language packages
        run: |
          Set-Location gces-build
          echo "Building language packages..."
          dotnet .\tools\Bundler\gces-language-package-bundler.dll .\Release\manifest.txt ..\WynFiles $env:BUILD_VERSION True .\Release\resource-order
          #azcopy cp ..\WynFiles\language-package-*-EN.zip "$env:DEV_ARTIFACTS_URL/$env:PRODUCT_VERSION/$env:BUILD_VERSION/lang-pack/?$env:DEV_ARTIFACTS_SAS_TOKEN"
          Pop-Location

      - name: Prepare content
        run: |
          Set-Location windows-installer
          $pver = $env:PRODUCT_VERSION -replace '\.', '_'
          dotnet .\PrepareFiles.dll ..\WynFiles .\InstallImage\Files $env:BUILD_VERSION wynbuildversion$pver_en
          Pop-Location

      - name: Push gces-build tag
        run: |
          Set-Location gces-build
          Set-Content -Path .\Release\version.txt -NoNewLine -Value $env:BUILD_VERSION
          git config --global user.name "gitea-actions"
          git config --global user.email "<EMAIL>"
          $diff = git diff --named-only HEAD -- Release/manifest.txt
          if ($diff) {
            git add Release/manifest.txt
          }
          $diff = git diff --named-only HEAD -- Release/resource-order/en-resource-order.json
          if ($diff) {
            git add Release/resource-order/*
          }
          git add Release/version.txt
          git commit -m "upgrade version - gitea-actions"
          git push https://${{ secrets.token }}@code.wynenterprise.io/wyn-core/gces-build.git
          git tag $env:BUILD_VERSION
          git push "https://${{ secrets.token }}@code.wynenterprise.io/wyn-core/gces-build.git" $env:BUILD_VERSION
          Pop-Location

      - name: Push windows-installer tag
        run: |
          Set-Location windows-installer
          git config --global user.name "gitea-actions"
          git config --global user.email "<EMAIL>"
          git add .\Setup\PreProcessVar.wxi
          git add .\GrapeCityInstaller\PreProcessVar.wxi
          git add .\MSI\CotWorker\Variable.wxi
          git add .\MSI\ReportWorker\Variable.wxi
          git add .\MSI\Server\Variable.wxi
          git add .\MSI\Prepare\Variable.wxi
          git add .\MSI\Finish\Variable.wxi
          git add .\MSI\JavaMemoryService\Variable.wxi
          git add .\MSI\JavaDataSourceService\Variable.wxi
          git add .\MSI\DashboardWorker\Variable.wxi
          git add .\MSI\SchedulerService\Variable.wxi
          git commit -m "CI new version $env:BUILD_VERSION"
          git push "https://${{ secrets.token }}@code.wynenterprise.io/wyn-core/gces-installer-windows2.git"
          git tag $env:BUILD_VERSION
          git push "https://${{ secrets.token }}@code.wynenterprise.io/wyn-core/gces-installer-windows2.git" $env:BUILD_VERSION
          Pop-Location

      - name: Build custom action
        run: |
          Set-Location windows-installer
          dotnet .\RemoveUnusedRuntimesFile.dll
          MSBuild.exe BuildCustomAction.xml
          Pop-Location

      - name: Update file name
        run: |
          Set-Location windows-installer
          .\updatename.ps1
          Pop-Location

      - name: Build installer
        run: |
          Set-Location windows-installer
          MSBuild.exe BuildMsiCotWorker.xml
          MSBuild.exe BuildMsiFinish.xml
          MSBuild.exe BuildMsiPrepare.xml
          MSBuild.exe BuildMsiReportWorker.xml
          MSBuild.exe BuildMsiServer.xml
          MSBuild.exe BuildMsiSetup.xml
          MSBuild.exe BuildMsiJavaDataSourceService.xml
          MSBuild.exe BuildMsiJavaMemoryService.xml
          MSBuild.exe BuildMsiDashboardWorker.xml
          MSBuild.exe BuildMsiSchedulerService.xml
          MSBuild.exe BuildInstaller.xml
          Pop-Location

      - name: Upload installer
        run: |
          Set-Location windows-installer
          echo "Uploading installer..."
          #azcopy cp "GrapeCityInstaller\\bin\\x64\\Release-Offline\\WynEnterprise-x64-$env:BUILD_VERSION.exe" "$env:DEV_ARTIFACTS_URL/$env:PRODUCT_VERSION/$env:BUILD_VERSION/WynEnterprise-x64-offline-$env:BUILD_VERSION.exe?$env:DEV_ARTIFACTS_SAS_TOKEN"
          Pop-Location

      # - name: Upload msi
      #   run: |
      #     Set-Location windows-installer
      #     echo "Uploading msi..."
      #     azcopy cp "InstallImage\\SetupMsi\\*.msi" "https://wyndelivery.blob.core.windows.net/wynartifacts/BI/installation/windows/online/msi?$env:CDN_SAS_TOKEN" --include-pattern "*.msi" --recursive
      #     azcopy cp "GrapeCityInstaller\\bin\\x64\\Release-Online\\WynEnterprise-x64-$env:BUILD_VERSION.exe" "$env:DEV_ARTIFACTS_URL/$env:PRODUCT_VERSION/$env:BUILD_VERSION/WynEnterprise-x64-online-$env:BUILD_VERSION.exe?$env:DEV_ARTIFACTS_SAS_TOKEN" --include-pattern "*.msi" --recursive
      #     Pop-Location
