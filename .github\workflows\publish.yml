name: Publish Artifacts

on:
  workflow_dispatch:
    inputs:
      publish_type:
        description: 'The publish type'
        required: true
        type: choice
        options:
          - 'beta'
          - 'release'
      product_version:
        description: 'The product version'
        required: true
        type: choice
        options:
          - '9.0'
          - '8.1'
      publish_version:
        description: 'The publish version'
        required: true
        type: string

env:
  DEV_ARTIFACTS_URL: ${{ variables.DEV_ARTIFACTS_URL }}
  DEV_ARTIFACTS_SAS_TOKEN: ${{ secrets.DEV_ARTIFACTS_SAS_TOKEN }}
  CDN_URL: ${{ variables.CDN_URL }}
  CDN_SAS_TOKEN: ${{ secrets.CDN_SAS_TOKEN }}
  DEV_DOCKER_REGISTRY: ${{ variables.DEV_DOCKER_REGISTRY }}
  DEV_DOCKER_USERNAME: ${{ secrets.DEV_DOCKER_USERNAME }}
  DEV_DOCKER_PASSWORD: ${{ secrets.DEV_DOCKER_PASSWORD }}
  BETA_DOCKER_REGISTRY: ${{ variables.BETA_DOCKER_REGISTRY }}
  BETA_DOCKER_USERNAME: ${{ secrets.BETA_DOCKER_USERNAME }}
  BETA_DOCKER_PASSWORD: ${{ secrets.BETA_DOCKER_PASSWORD }}
  PROD_DOCKER_USERNAME: ${{ secrets.PROD_DOCKER_USERNAME }}
  PROD_DOCKER_PASSWORD: ${{ secrets.PROD_DOCKER_PASSWORD }}
  GPG_KEY_PSWD: ${{ secrets.GPG_KEY_PSWD }}
  GH_PAT: ${{ secrets.GITHUB_TOKEN }}
  PRODUCT_VERSION: ${{ github.event.inputs.product_version }}
  VERSION_NUMBER: ${{ github.event.inputs.publish_version }}

jobs:
  publish-beta:
    if: ${{ github.event.inputs.publish_type == 'beta' }}
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    
    steps:
      - name: Check input
        run: |
          if [[ "$PRODUCT_VERSION" == "" ]]; then
            echo "no product version detected."
            exit 1
          fi
          if [[ "$VERSION_NUMBER" == "" ]]; then
            echo "no publish version detected."
            exit 1
          fi
          if [[ "$VERSION_NUMBER" != "$PRODUCT_VERSION.00"* ]]; then
            echo "the publish version $VERSION_NUMBER is not compatible with the product version $PRODUCT_VERSION."
            exit 1
          fi

      - name: Checkout gces-build
        uses: actions/checkout@v4
        with:
          ref: refs/heads/release/v$PRODUCT_VERSION-en
          path: gces-build

      - name: Checkout k8s-deployment
        uses: actions/checkout@v4
        with:
          repository: wyn-core/gces-k8s-deployment
          ref: refs/heads/release/v$PRODUCT_VERSION
          path: k8s-deployment
          token: $GH_PAT

      - name: Setup zip
        run: |
          apt-get update
          apt-get install -y apt-utils
          apt-get install -y zip

      - name: Setup azcopy
        run: |
          azcopy -v > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "azcopy is already installed"
          else
            echo "installing azcopy..."
            cd /var/tmp
            curl -sL https://aka.ms/downloadazcopy-v10-linux | tar -xz
            cp ./azcopy_linux_amd64_*/azcopy /usr/bin/
            cd -
          fi

      - name: Upload windows installers
        run: |
          SRC_OFFLINE="$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$VERSION_NUMBER/WynEnterprise-x64-offline-$VERSION_NUMBER.exe?$DEV_ARTIFACTS_SAS_TOKEN"
          DST_OFFLINE="$CDN_URL/BI/beta/windows/offline/WynEnterprise-x64-offline-$VERSION_NUMBER.exe?$CDN_SAS_TOKEN"
          echo "uploading WynEnterprise-x64-offline-$VERSION_NUMBER.exe..."
          azcopy copy "$SRC_OFFLINE" "$DST_OFFLINE"
          SRC_ONLINE="$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$VERSION_NUMBER/WynEnterprise-x64-online-$VERSION_NUMBER.exe?$DEV_ARTIFACTS_SAS_TOKEN"
          DST_ONLINE="$CDN_URL/BI/beta/windows/online/WynEnterprise-x64-online-$VERSION_NUMBER.exe?$CDN_SAS_TOKEN"
          echo "uploading WynEnterprise-x64-online-$VERSION_NUMBER.exe..."
          azcopy copy "$SRC_ONLINE" "$DST_ONLINE"

      - name: Import gpg key
        run: |
          gpg --list-secret-keys | grep <EMAIL> > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "gpg key is already imported"
          else
            echo "importing gpg key..."
            apt-get update && apt-get isntall -y wget
            wget https://wynartifacts.oss-cn-hangzhou.aliyuncs.com/dev/wyn_private.key
            gpg --import --pinentry-mode loopback --batch --passphrase $GPG_KEY_PSWD wyn_private.key
            rm -rf ./wyn_private.key
            gpg --list-secret-keys | grep <EMAIL> > /dev/null 2>&1
            if [ "0" == "$?" ]; then
              echo "import gpg key successfully"
              exit 0
            else
              echo "import gpg key failed"
              exit 1
            fi
          fi

      - name: Build apt repo
        run: |
          DEV_ADDR="$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$VERSION_NUMBER"
          CDN_ADDR="$CDN_URL/BI/beta/apt/$PRODUCT_VERSION"
          cd gces-build/EN/Linux/apt
          echo "building apt repo..."
          bash ./push-to-cloud.sh $VERSION_NUMBER $DEV_ADDR $DEV_ARTIFACTS_SAS_TOKEN $CDN_ADDR $CDN_SAS_TOKEN

      - name: Build createrepo
        run: |
          createrepo --version > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "createrepo is already installed"
          else
            apt-get update
            apt-get install -y libcurl4-openssl-dev libbz2-dev libxml2-dev libssl-dev zlib1g-dev pkg-config libglib2.0-dev liblzma-dev libsqlite3-dev librpm-dev libzstd-dev python3-dev cmake
            cd /var/tmp
            git clone https://github.com/rpm-software-management/createrepo_c
            cd createrepo_c
            mkdir build
            cd build
            cmake .. -DWITH_ZCHUNK=NO -DWITH_LIBMODULEMD=NO
            make -j
            mkdir -p /opt/createrepo
            cp ./src/libcreaterepo_c.so* /opt/createrepo/
            cp ./src/createrepo_c /opt/createrepo/
            ln -sf /opt/createrepo/createrepo_c /usr/bin/createrepo
            createrepo --version
            if [ "0" == "$?" ];then
              echo "install createrepo successfully."
              exit 0
            else
              echo "install createrepo failed."
              exit 1
            fi
          fi
          
      - name: Build yum repo
        run: |
          DEV_ADDR="$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$VERSION_NUMBER"
          CDN_ADDR="$CDN_URL/BI/beta/yum/$PRODUCT_VERSION"
          cd gces-build/EN/Linux/yum
          echo "building yum repo..."
          bash ./push-to-cloud.sh $VERSION_NUMBER $DEV_ADDR $DEV_ARTIFACTS_SAS_TOKEN $CDN_ADDR $CDN_SAS_TOKEN

      - name: Setup Docker Buildx
        run: |
          docker buildx ls | grep linux/arm64 > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "docker buildx is already setupped"
          else
            echo "setting up docker buildx..."
            apt-get update
            apt-get install -y qemu-user-static binfmt-support
            docker run --rm --privileged multiarch/qemu-user-static --reset -p yes
            docker buildx ls | grep linux/arm64
            if [ "0" == "$?" ]; then
              echo "docker buildx is setupped successfully"
            else
              echo "setup docker buildx failed"
              exit 1
            fi
          fi

      - name: Push x64 docker image
        run: |
          DEV_IMAGE_NAME="wyn-$PRODUCT_VERSION-en-dev"
          BETA_IMAGE_NAME="wyn-enterprise-beta"
          cd gces-build/EN/Linux/Docker
          echo "pushing x64 docker image..."
          bash ./tag-and-push.sh $DEV_IMAGE_NAME $BETA_IMAGE_NAME $VERSION_NUMBER $DEV_DOCKER_REGISTRY "" $DEV_DOCKER_USERNAME $DEV_DOCKER_PASSWORD $BETA_DOCKER_REGISTRY "" $BETA_DOCKER_USERNAME $BETA_DOCKER_PASSWORD
          docker manifest inspect $BETA_DOCKER_REGISTRY/$BETA_IMAGE_NAME:$VERSION_NUMBER > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "push x64 docker image successfully"
            exit 0
          else
            echo "push x64 docker image failed"
            exit 1
          fi

      - name: Push arm64 docker image
        run: |
          DEV_IMAGE_NAME="wyn-$PRODUCT_VERSION-en-arm64-dev"
          BETA_IMAGE_NAME="wyn-enterprise-arm64-beta"
          cd gces-build/EN/Linux/Docker
          echo "pushing arm64 docker image..."
          bash ./tag-and-push.sh $DEV_IMAGE_NAME $BETA_IMAGE_NAME $VERSION_NUMBER $DEV_DOCKER_REGISTRY "--platform linux/arm64" $DEV_DOCKER_USERNAME $DEV_DOCKER_PASSWORD $BETA_DOCKER_REGISTRY "--platform linux/arm64" $BETA_DOCKER_USERNAME $BETA_DOCKER_PASSWORD
          docker manifest inspect $BETA_DOCKER_REGISTRY/$BETA_IMAGE_NAME:$VERSION_NUMBER > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "push arm64 docker image successfully"
            exit 0
          else
            echo "push arm64 docker image failed"
            exit 1
          fi

      - name: Push k8s docker image
        run: |
          DEV_IMAGE_NAME="wyn-$PRODUCT_VERSION-en-k8s-dev"
          BETA_IMAGE_NAME="wyn-enterprise-k8s-beta"
          cd gces-build/EN/Linux/Docker
          echo "pushing k8s docker image..."
          bash ./tag-and-push.sh $DEV_IMAGE_NAME $BETA_IMAGE_NAME $VERSION_NUMBER $DEV_DOCKER_REGISTRY "--platform linux/amd64,linux/arm64" $DEV_DOCKER_USERNAME $DEV_DOCKER_PASSWORD $BETA_DOCKER_REGISTRY "--platform linux/amd64,linux/arm64" $BETA_DOCKER_USERNAME $BETA_DOCKER_PASSWORD
          docker manifest inspect $BETA_DOCKER_REGISTRY/$BETA_IMAGE_NAME:$VERSION_NUMBER > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "push k8s docker image successfully"
            exit 0
          else
            echo "push k8s docker image failed"
            exit 1
          fi

      - name: Setup helm
        run: |
          helm version
          if [ "0" == "$?" ]; then
            echo "helm already installed"
            exit 0
          else
            apt-get update
            apt-get install -y curl apt-transport-https
            curl https://baltocdn.com/helm/signing.asc | gpg --dearmor | tee /usr/share/keyrings/helm.gpg > /dev/null
            echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/helm.gpg] https://baltocdn.com/helm/stable/debian/ all main" | tee /etc/apt/sources.list.d/helm-stable-debian.list
            apt-get update
            apt-get install -y helm

            helm version
            if [ "0"=="$?" ]
            then
              echo "helm installed successfully"
              exit 0
            else
              echo "install helm failed."
              exit 1
            fi
          fi

      - name: Push helm chart
        run: |
          cd k8s-deployments
          echo "pushing helm chart..."
          bash ./publish-helm-chart-en.sh $VERSION_NUMBER "https://cdn.wynenterprise.io/BI/beta/helm-charts" "$CDN_URL/BI/beta/helm-charts" $CDN_SAS_TOKEN

  publish-release:
    if: ${{ github.event.inputs.publish_type == 'release' }}
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    
    steps:
      - name: Checkout gces-build
        uses: actions/checkout@v4
        with:
          ref: refs/heads/release/v$PRODUCT_VERSION-en
          path: gces-build

      - name: Checkout k8s-deployment
        uses: actions/checkout@v4
        with:
          repository: wyn-core/gces-k8s-deployments
          ref: refs/heads/release/v$PRODUCT_VERSION
          path: k8s-deployments
          token: $GH_PAT

      - name: Setup zip
        run: |
          apt-get update
          apt-get install -y apt-utils
          apt-get install -y zip

      - name: Setup azcopy
        run: |
          azcopy -v > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "azcopy is already installed"
          else
            echo "installing azcopy..."
            cd /var/tmp
            curl -sL https://aka.ms/downloadazcopy-v10-linux | tar -xz
            cp ./azcopy_linux_amd64_*/azcopy /usr/bin/
            cd -
          fi

      - name: Upload windows installer
        run: |
          SRC_OFFLINE="$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$VERSION_NUMBER/WynEnterprise-x64-offline-$VERSION_NUMBER.exe?$DEV_ARTIFACTS_SAS_TOKEN"
          DST_OFFLINE="$CDN_URL/BI/installation/windows/offline/WynEnterprise-x64-offline-$VERSION_NUMBER.exe?$CDN_SAS_TOKEN"
          echo "uploading windows offline installer..."
          azcopy copy $SRC_OFFLINE $DST_OFFLINE
          SRC_ONLINE="$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$VERSION_NUMBER/WynEnterprise-x64-online-$VERSION_NUMBER.exe?$DEV_ARTIFACTS_SAS_TOKEN"
          DST_ONLINE="$CDN_URL/BI/installation/windows/online/WynEnterprise-x64-online-$VERSION_NUMBER.exe?$CDN_SAS_TOKEN"
          echo "uploading windows online installer..."
          azcopy copy $SRC_ONLINE $DST_ONLINE

      - name: Build apt repo
        run: |
          DEV_ADDR="$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$VERSION_NUMBER"
          CDN_ADDR="$CDN_URL/BI/installation/apt/$PRODUCT_VERSION"
          cd gces-build/EN/Linux/apt
          echo "building apt repo..."
          bash ./push-to-cloud.sh $VERSION_NUMBER $DEV_ADDR $DEV_ARTIFACTS_SAS_TOKEN $CDN_ADDR $CDN_SAS_TOKEN
      
      - name: Setup rpm build environment
        run: |
          rpmbuild --version > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "rpmbuild is already installed"
          else
            echo "setting up rpm build environment..."
            apt-get update
            apt-get install -y rpm
            mkdir -p /var/rpmbuild/{BUILD,BUILDROOT,RPMS/x86_64,SOURCES,SPECS,SRPMS,tmp}
            echo "%_topdir /var/rpmbuild" > ~/.rpmmacros
            echo "%_tmppath %{_topdir}/tmp" >> ~/.rpmmacros
          fi

      - name: Build yum repo
        run: |
          DEV_ADDR="$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$VERSION_NUMBER"
          CDN_ADDR="$CDN_URL/BI/installation/yum/$PRODUCT_VERSION"
          cd gces-build/EN/Linux/yum
          echo "building yum repo..."
          bash ./push-to-cloud.sh $VERSION_NUMBER $DEV_ADDR $DEV_ARTIFACTS_SAS_TOKEN $CDN_ADDR $CDN_SAS_TOKEN

      - name: Setup Docker Buildx
        run: |
          docker buildx ls | grep linux/arm64 > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "docker buildx is already setupped"
          else
            echo "setting up docker buildx..."
            apt-get update
            apt-get install -y qemu-user-static binfmt-support
            docker run --rm --privileged multiarch/qemu-user-static --reset -p yes
            docker buildx ls | grep linux/arm64
            if [ "0" == "$?" ]; then
              echo "docker buildx is setupped successfully"
            else
              echo "setup docker buildx failed"
              exit 1
            fi
          fi

      - name: Push x64 docker image
        run: |
          DEV_IMAGE_NAME="wyn-$PRODUCT_VERSION-en-dev"
          RELEASE_IMAGE_NAME="wyn-enterprise"
          cd gces-build/EN/Linux/Docker
          echo "pushing x64 docker image..."
          bash ./tag-and-push.sh $DEV_IMAGE_NAME $RELEASE_IMAGE_NAME $VERSION_NUMBER $DEV_DOCKER_REGISTRY "" $DEV_DOCKER_USERNAME $DEV_DOCKER_PASSWORD "" wynenterprise $PROD_DOCKER_USERNAME $PROD_DOCKER_PASSWORD
          docker manifest inspect $PROD_DOCKER_USERNAME/$RELEASE_IMAGE_NAME:$VERSION_NUMBER > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "push x64 docker image successfully"
            exit 0
          else
            echo "push x64 docker image failed"
            exit 1
          fi

      - name: Push arm64 docker image
        run: |
          DEV_IMAGE_NAME="wyn-$PRODUCT_VERSION-en-arm64-dev"
          RELEASE_IMAGE_NAME="wyn-enterprise-arm64"
          cd gces-build/EN/Linux/Docker
          echo "pushing arm64 docker image..."
          bash ./tag-and-push.sh $DEV_IMAGE_NAME $RELEASE_IMAGE_NAME $VERSION_NUMBER $DEV_DOCKER_REGISTRY "" $DEV_DOCKER_USERNAME $DEV_DOCKER_PASSWORD "" wynenterprise $PROD_DOCKER_USERNAME $PROD_DOCKER_PASSWORD
          docker manifest inspect $PROD_DOCKER_USERNAME/$RELEASE_IMAGE_NAME:$VERSION_NUMBER > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "push arm64 docker image successfully"
            exit 0
          else
            echo "push arm64 docker image failed"
            exit 1
          fi

      - name: Push k8s docker image
        run: |
          DEV_IMAGE_NAME="wyn-$PRODUCT_VERSION-en-k8s-dev"
          RELEASE_IMAGE_NAME="wyn-enterprise-k8s"
          cd gces-build/EN/Linux/Docker
          echo "pushing k8s docker image..."
          bash ./tag-and-push.sh $DEV_IMAGE_NAME $RELEASE_IMAGE_NAME $VERSION_NUMBER $DEV_DOCKER_REGISTRY "" $DEV_DOCKER_USERNAME $DEV_DOCKER_PASSWORD "" wynenterprise $PROD_DOCKER_USERNAME $PROD_DOCKER_PASSWORD
          docker manifest inspect $PROD_DOCKER_USERNAME/$RELEASE_IMAGE_NAME:$VERSION_NUMBER > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "push k8s docker image successfully"
            exit 0
          else
            echo "push k8s docker image failed"
            exit 1
          fi

      - name: Push k8s scripts
        run: |
          cd k8s-deployments
          echo "pushing k8s scripts..."
          bash ./publish-en.sh $VERSION_NUMBER "$CDN_URL/BI/installation/k8s" $CDN_SAS_TOKEN

      - name: Setup helm
        run: |
          helm version
          if [ "0" == "$?" ]; then
            echo "helm already installed"
            exit 0
          else
            apt-get update
            apt-get install -y curl apt-transport-https
            curl https://baltocdn.com/helm/signing.asc | gpg --dearmor | tee /usr/share/keyrings/helm.gpg > /dev/null
            echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/helm.gpg] https://baltocdn.com/helm/stable/debian/ all main" | tee /etc/apt/sources.list.d/helm-stable-debian.list
            apt-get update
            apt-get install -y helm
  
            helm version
            if [ "0"=="$?" ]
            then
              echo "helm installed successfully"
              exit 0
            else
              echo "install helm failed."
              exit 1
            fi
          fi

      - name: Push helm chart
        run: |
          cd k8s-deployments
          echo "pushing helm chart..."
          bash ./publish-helm-chart-en.sh $VERSION_NUMBER "https://cdn.wynenterprise.io/BI/installation/helm-charts" "$CDN_URL/BI/installation/helm-charts" $CDN_SAS_TOKEN

      - name: Upload language packages
        run: |
          DEV_PATH="$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$VERSION_NUMBER/lang-pack"
          CDN_PATH="$CDN_URL/BI/lang-pack/$VERSION_NUMBER"
          echo "uploading language packages..."
          azcopy copy "$DEV_PATH?$DEV_ARTIFACTS_SAS_TOKEN" "$CDN_PATH?$CDN_SAS_TOKEN" --recursive=true

      - name: Create new version
        run: |
          echo "create new version"
          URL="https://builds.wynenterprise.com/api/versions/create"
          ONLINE_DOWNLOAD_LINK="https://cdn.wynenterprise.io/BI/installation/windows/online/WynEnterprise-x64-online-$VERSION_NUMBER.exe"
          OFFLINE_DOWNLOAD_LINK="https://cdn.wynenterprise.io/BI/installation/windows/offline/WynEnterprise-x64-offline-$VERSION_NUMBER.exe"
          DATE="$(date +%F)"
          TIME="$(date +%T)"
          TOKEN="iaiYiKOKjQlldxQOK0i6RC2eQJv6GpLeZ41VlUGA1TAe0rOLYNJ8nkj7kniPEafNwOLWueC8yr6l3tCBWOf13Q=="
          DETAIL="Published By CI."
          DATA="{\"name\":\"$VERSION_NUMBER\",\"releaseTime\":\"$DATE $TIME\",\"published\":false,\"downloadLink\":\"$ONLINE_DOWNLOAD_LINK\",\"DownloadOfflineLink\":\"$OFFLINE_DOWNLOAD_LINK\",\"versionDetail\":\"$DETAIL\",\"summary\":\"summary\"}"
          STATUS_CODE=$(curl -o -I -L -s -w "%{http_code}" -X POST -H "token:$TOKEN" -H "Content-Type: application/json" --data "$DATA" "$URL")
          if [ $STATUS_CODE -ne 200 ]
          then
            echo "create new version failed with status code $STATUS_CODE"
            exit 1
          else
            echo "new version created successfully"
            exit 0
          fi




