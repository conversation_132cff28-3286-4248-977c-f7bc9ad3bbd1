#!/bin/bash

WYN_INSTALL_DIR="/opt/Wyn"
WYN_PID_FILE="/var/run/wyn.pid"

do_start()
{
  if [ ! -f $WYN_PID_FILE ];
  then
    cd $WYN_INSTALL_DIR/Monitor
    dotnet ServiceMonitor.dll > /dev/null 2>&1 &
    echo $! > $WYN_PID_FILE
  fi
}

do_stop()
{
  if [ -f $WYN_PID_FILE ];
  then
    pkill -F $WYN_PID_FILE
    sudo rm $WYN_PID_FILE
  fi
}

do_status()
{
  if [ -f $WYN_PID_FILE ];
  then
    echo "wyn is running"
  else
    echo "wyn is not running"
  fi
}

case "$1" in
  start)
    do_start
    ;;

  stop)
    do_stop
    ;;

  restart)
    do_stop
    do_start
    ;;

  status)
    do_status
    ;;

  *)
    echo "Usage: $0 {start|stop|restart|status}"
    exit 0
    ;;

esac