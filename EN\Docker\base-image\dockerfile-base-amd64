FROM mcr.microsoft.com/dotnet/aspnet:8.0-jammy

ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && \
    apt-get install -y wget gnupg2 apt-transport-https ca-certificates nginx unixodbc tzdata && \
    wget --no-check-certificate --output-document=- https://www.monetdb.org/downloads/MonetDB-GPG-KEY.gpg | apt-key add - && \
    wget --output-document=- https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - && \
    ln -fs /usr/share/zoneinfo/Etc/UTC /etc/localtime

RUN echo 'Acquire::https::www.monetdb.org::Verify-Peer "false";' > /etc/apt/apt.conf.d/99monetdb-cert && \
    echo "deb https://www.monetdb.org/downloads/deb/ jammy-20240924145810077177068 monetdb" > /etc/apt/sources.list.d/monetdb.list && \
    echo "deb-src https://www.monetdb.org/downloads/deb/ jammy-20240924145810077177068 monetdb" >> /etc/apt/sources.list.d/monetdb.list && \
    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list && \
    apt-get update && \
    apt-get install -y libmonetdb-stream25=11.43.23 libmonetdb-client25=11.43.23 libmonetdb25=11.43.23 monetdb-client=11.43.23 monetdb5-server=11.43.23 monetdb5-sql=11.43.23 google-chrome-stable

