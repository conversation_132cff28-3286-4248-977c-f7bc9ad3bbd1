﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace gces_artifacts_downloader
{
	public class ArtifactsDownloader
	{
		private readonly string _manifestFilePath;
		private readonly string _outoutPath;

		protected ArtifactsDownloader() { }
		public ArtifactsDownloader(string manifestFilePath, string outputPath)
		{
			_manifestFilePath = manifestFilePath;
			_outoutPath = outputPath;
		}

		public List<(string repoName, string artifactName)> GetNeedDownloadedArtifacts()
		{
			var downloadedFiles = new List<string>();
			var targetFiles = File.ReadLines(_manifestFilePath, Encoding.UTF8)
				.Where(line => !string.IsNullOrEmpty(line))
				.Select(repoNameWithArtifact =>
				{
					var segs = repoNameWithArtifact.Split(new char[] { ' ', '\t', '\n' }).Where(s => !string.IsNullOrEmpty(s)).Select(s => s.Trim()).ToList();
					var repoName = segs[0];
					var artifact = segs[1];
					var targetFile = Path.Combine(_outoutPath, artifact);
					if (File.Exists(targetFile))
					{
						downloadedFiles.Add(artifact);
					}
					return (RepoName: repoName, OutputFile: artifact);
				}).ToList();
			var files = Directory.GetFiles(_outoutPath);
			foreach (var file in files)
			{
				var fileName = file.Substring(file.LastIndexOf('\\') + 1, file.Length - file.LastIndexOf('\\') - 1);
				if (!targetFiles.Any(f => f.OutputFile.Equals(fileName, StringComparison.OrdinalIgnoreCase)))
				{
					File.Delete(file);
				}
			}
			var tups = targetFiles.Where(f => !downloadedFiles.Any(d => d.Equals(f.OutputFile, StringComparison.OrdinalIgnoreCase))).ToList();
			return tups;
		}

		public bool DownloadArtifacts(IEnumerable<(string repoName, string artifactName)> tups)
		{
			var tasks = new List<Task<bool>>();
			foreach (var tup in tups)
			{
				var task = Task<bool>.Run(() =>
				{
					var watch = Stopwatch.StartNew();
					using (var client = new HttpClient())
					{
						client.DefaultRequestHeaders.Add("Authorization", Consts.DefaultAuthorization);
						var url = $"{Consts.DefaultRepositoryUri}/{tup.repoName}/downloads/{tup.artifactName}";
						var outputFile = Path.Combine(_outoutPath, tup.artifactName);
						var retryCount = 0;
						while (retryCount++ < 10)
						{
							Console.WriteLine($"Downloading {tup.artifactName} ...");

							try
							{
								using (var fs = new FileStream(outputFile, FileMode.Create, FileAccess.Write))
								{
									client.GetStreamAsync(url).Result.CopyTo(fs);
								}
								watch.Stop();
								Console.WriteLine($"Downloaded {tup.artifactName}. Time cost={watch.ElapsedMilliseconds}");
								break;
							}
							catch (Exception ex)
							{
								Console.WriteLine($"Url={url}  Retry={retryCount}");
								Console.WriteLine($"Exception={ex.ToString()}");
							}
						}
						if (retryCount >= 10)
						{
							Console.WriteLine("One of tasks reached max retry count.");
							if (File.Exists(outputFile))
							{
								File.Delete(outputFile);
							}
							return false;
						}
					}
					return true;
				});
				tasks.Add(task);
			}

			Task.WaitAll(tasks.ToArray(), TimeSpan.FromMinutes(60));

			if (tasks.All(t => t.IsCompletedSuccessfully && t.Result))
			{
				Console.WriteLine("Downloading tasks finished.");
				return true;
			}
			else
			{
				Console.WriteLine("Downloading tasks failed.");
				return false;
			}
		}
	}
}
