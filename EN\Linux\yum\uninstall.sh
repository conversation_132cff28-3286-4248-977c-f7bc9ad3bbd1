#!/bin/bash

#====================================== GLOBAL VARIABLES ======================================#

INSTALL_TARGET_DIR="/opt"
WYN_INSTALL_DIR="$INSTALL_TARGET_DIR/Wyn"
POSTGRES_INSTALL_DIR="$INSTALL_TARGET_DIR/postgresql"
WYN_DATABASE_SERVICE="wyn-database"
SILENT_INSTALLATION=false
FORCE_REMOVE_WYN=false
REMOVE_DATABASE=false
LOG_FILE="uninstall.log"

#====================================== FUNCTIONS ======================================#

prtMsg() {
  for msg in "$@"
  do
    if $SILENT_INSTALLATION ; then
      echo "$(date) $msg" >> $LOG_FILE
    else
      echo "$msg"
    fi
  done
}

# catch "ctrl-c" and exit the uninstallation process
trap_ctrlc() {
  prtMsg ""
  prtMsg "Ctrl-C caught, canceling the uninstallation..."
  stty echo
  exit $EXIT_CODE_CTRL_C
}

parseArguments() {
  OPTIND=1
  local ret=0
  while getopts sfrh opt; do
    case $opt in
      s)
        SILENT_INSTALLATION=true
        ;;
      f)
        FORCE_REMOVE_WYN=true
        ;;
      r)
        REMOVE_DATABASE=true
        ;;
      h)
        ret=1
        ;;
      *)
        ret=1
        ;;
    esac
  done
  return $ret
}

showUsage() {
  prtMsg "Usage: ${0##*/} [-s] [-f] [-r] [-h]
  -s    Silent installation.
  -f    Force remove all the contents of Wyn and the database data.
  -r    Remove the built-in database and the monetdb.
  -h    Help information."
}

# input: $1 - Wyn version
uninstallWyn() {
  prtMsg "Uninstalling wyn..."
  local isYumAvailable=false
  yum --version > /dev/null 2>&1
  if [ "0" == "$?" ]; then
    if $SILENT_INSTALLATION ; then
      sudo yum erase -y wyn-enterprise >> $LOG_FILE 2>&1
    else
      sudo yum erase wyn-enterprise
    fi
  else
    if $SILENT_INSTALLATION ; then
      sudo zypper remove -y wyn-enterprise >> $LOG_FILE 2>&1
    else
      sudo zypper remove wyn-enterprise
    fi
  fi
  sudo rm /opt/Wyn/.modules > /dev/null 2>&1
  sudo rm -rf /opt/Wyn/Monitor/conf > /dev/null 2>&1
  sudo rm -rf /opt/Wyn/Tools/RegisterTrialInfo > /dev/null 2>&1
  sudo rm -rf /opt/Wyn/jre > /dev/null 2>&1
}

uninstallDatabase() {
  if [ -d "$POSTGRES_INSTALL_DIR" ]; then
    prtMsg "Removing database..."
    # stop database service
    if $SILENT_INSTALLATION ; then
      sudo systemctl stop $WYN_DATABASE_SERVICE >> $LOG_FILE 2>&1
    else
      sudo systemctl stop $WYN_DATABASE_SERVICE
    fi
    # remove database service
    if [ -f "/lib/systemd/system/wyn-database.service" ]; then
      sudo rm "/lib/systemd/system/wyn-database.service"
    fi
    if [ -f "/usr/lib/systemd/system/wyn-database.service" ]; then
      sudo rm "/usr/lib/systemd/system/wyn-database.service"
    fi
    if [ -L "/etc/systemd/system/multi-user.target.wants/wyn-database.service" ]; then
      sudo rm "/etc/systemd/system/multi-user.target.wants/wyn-database.service"
    fi
    if [ -f "/etc/init.d/wyn-database" ]; then
      sudo rm "/etc/init.d/wyn-database"
    fi
    # remove database content
    if $FORCE_REMOVE_WYN ; then
      if [ -d "$POSTGRES_INSTALL_DIR" ]; then
        sudo rm -rf "$POSTGRES_INSTALL_DIR" > /dev/null 2>&1
      fi
      if [ -d "$WYN_INSTALL_DIR/wyndbs" ]; then
        sudo rm -rf "$WYN_INSTALL_DIR/wyndbs" > /dev/null 2>&1
      fi
    else
      sudo rm -rf "$POSTGRES_INSTALL_DIR/pgsql"
    fi
  fi
}

uninstallMonetDB() {
  monetdbd --version > /dev/null 2>&1
  if [ "0" == "$?" ]; then
    if $SILENT_INSTALLATION ; then
      sudo systemctl stop monetdbd >> $LOG_FILE 2>&1
      if [ -f "/lib/systemd/system/monetdbd.service" ]; then
        sudo rm "/lib/systemd/system/monetdbd.service"
      fi
      if [ -f "/usr/lib/systemd/system/monetdbd.service" ]; then
        sudo rm "/usr/lib/systemd/system/monetdbd.service"
      fi
      if [ -L "/etc/systemd/system/multi-user.target.wants/monetdbd.service" ]; then
        sudo rm "/etc/systemd/system/multi-user.target.wants/monetdbd.service"
      fi
      sudo systemctl daemon-reload >> $LOG_FILE 2>&1
      sudo rm -rf /etc/logrotate.d/monetdbd >> $LOG_FILE 2>&1
      sudo rm -rf /run/monetdb >> $LOG_FILE 2>&1
      sudo rm -rf /usr/bin/mclient >> $LOG_FILE 2>&1
      sudo rm -rf /usr/bin/monetdb >> $LOG_FILE 2>&1
      sudo rm -rf /usr/bin/monetdbd >> $LOG_FILE 2>&1
      sudo rm -rf /usr/bin/mserver5 >> $LOG_FILE 2>&1
      sudo rm -rf /usr/bin/msqldump >> $LOG_FILE 2>&1
      sudo rm -rf /usr/lib/sysusers.d/monetdb.conf >> $LOG_FILE 2>&1
      sudo rm -rf /usr/lib/tmpfiles.d/monetdbd.conf >> $LOG_FILE 2>&1
      sudo rm -rf /usr/lib64/monetdb5 >> $LOG_FILE 2>&1
      sudo rm -rf /usr/lib64/libmapi.* >> $LOG_FILE 2>&1
      sudo rm -rf /usr/lib64/libmonetdb* >> $LOG_FILE 2>&1
      sudo rm -rf /usr/share/doc/MonetDB* >> $LOG_FILE 2>&1
      sudo rm -rf /usr/share/licenses/MonetDB* >> $LOG_FILE 2>&1
      sudo rm -rf /usr/share/man/man1/mclient.1.gz >> $LOG_FILE 2>&1
      sudo rm -rf /usr/share/man/man1/monetdb.1.gz >> $LOG_FILE 2>&1
      sudo rm -rf /usr/share/man/man1/monetdbd.1.gz >> $LOG_FILE 2>&1
      sudo rm -rf /usr/share/man/man1/mserver5.1.gz >> $LOG_FILE 2>&1
      sudo rm -rf /usr/share/man/man1/msqldump.1.gz >> $LOG_FILE 2>&1
      sudo rm -rf /var/lib/monetdb >> $LOG_FILE 2>&1
      sudo rm -rf /var/log/monetdb >> $LOG_FILE 2>&1
    else
      sudo systemctl stop monetdbd > /dev/null 2>&1
      if [ -f "/lib/systemd/system/monetdbd.service" ]; then
        sudo rm "/lib/systemd/system/monetdbd.service"
      fi
      if [ -f "/usr/lib/systemd/system/monetdbd.service" ]; then
        sudo rm "/usr/lib/systemd/system/monetdbd.service"
      fi
      if [ -L "/etc/systemd/system/multi-user.target.wants/monetdbd.service" ]; then
        sudo rm "/etc/systemd/system/multi-user.target.wants/monetdbd.service"
      fi
      sudo systemctl daemon-reload > /dev/null 2>&1
      sudo rm -rf /etc/logrotate.d/monetdbd > /dev/null 2>&1
      sudo rm -rf /run/monetdb > /dev/null 2>&1
      sudo rm -rf /usr/bin/mclient > /dev/null 2>&1
      sudo rm -rf /usr/bin/monetdb > /dev/null 2>&1
      sudo rm -rf /usr/bin/monetdbd > /dev/null 2>&1
      sudo rm -rf /usr/bin/mserver5 > /dev/null 2>&1
      sudo rm -rf /usr/bin/msqldump > /dev/null 2>&1
      sudo rm -rf /usr/lib/sysusers.d/monetdb.conf > /dev/null 2>&1
      sudo rm -rf /usr/lib/tmpfiles.d/monetdbd.conf > /dev/null 2>&1
      sudo rm -rf /usr/lib64/monetdb5 > /dev/null 2>&1
      sudo rm -rf /usr/lib64/libmapi.* > /dev/null 2>&1
      sudo rm -rf /usr/lib64/libmonetdb* > /dev/null 2>&1
      sudo rm -rf /usr/share/doc/MonetDB* > /dev/null 2>&1
      sudo rm -rf /usr/share/licenses/MonetDB* > /dev/null 2>&1
      sudo rm -rf /usr/share/man/man1/mclient.1.gz > /dev/null 2>&1
      sudo rm -rf /usr/share/man/man1/monetdb.1.gz > /dev/null 2>&1
      sudo rm -rf /usr/share/man/man1/monetdbd.1.gz > /dev/null 2>&1
      sudo rm -rf /usr/share/man/man1/mserver5.1.gz > /dev/null 2>&1
      sudo rm -rf /usr/share/man/man1/msqldump.1.gz > /dev/null 2>&1
      sudo rm -rf /var/lib/monetdb > /dev/null 2>&1
      sudo rm -rf /var/log/monetdb > /dev/null 2>&1
    fi
    if $FORCE_REMOVE_WYN ; then
      sudo rm -rf /var/monetdb5 > /dev/null 2>&1
    fi
  fi
}

#====================================== INSTALLATION ======================================#

trap "trap_ctrlc" INT

# parse and check the parameters
parseArguments $@
if [ "1" == "$?" ]; then
  showUsage
  exit 1
fi
if $SILENT_INSTALLATION ; then
  prtMsg "-------------------- UNINSTALLATION START --------------------"
fi

# uninstall Wyn
uninstallWyn

# remove all the contents
if $FORCE_REMOVE_WYN ; then
  sudo rm -rf "$WYN_INSTALL_DIR"
fi

# remove database
if $REMOVE_DATABASE ; then
  uninstallDatabase
  uninstallMonetDB
fi

prtMsg "Finished."

exit 0
