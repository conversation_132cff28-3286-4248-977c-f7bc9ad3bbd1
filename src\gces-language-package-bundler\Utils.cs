﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace gces_language_package_bundler
{
	internal static class Utils
	{
		public static readonly string ResourceConfigEntryName = "language-resources.json";

		internal static void AddManifest(string zipPath, string edition, string lng, string wyn_version)
		{
			using FileStream packageStream = new FileStream(zipPath, FileMode.Open);
			using ZipArchive lngPackFile = new ZipArchive(packageStream, ZipArchiveMode.Update);

			var manifestEntry = lngPackFile.CreateEntry("manifest.txt");
			using var manifestStream = manifestEntry.Open();
			var manifest = new PackageManifest(edition, $"language-package-{lng}", lng, wyn_version);
			var manifestString = JsonSerializer.Serialize(manifest, new JsonSerializerOptions() { WriteIndented = true });
			using var streamWriter = new StreamWriter(manifestStream);
			streamWriter.Write(manifestString);
		}

		internal static string BundleLanguagePackage(
			string lng,
			string artifacts_dir,
			List<string> lngArtifacts,
			List<(string RepoName, string OutputFile)> targetFiles)
		{
			Console.WriteLine($"bundle language-package-{lng}.zip ...");
			var lngPackFilePath = Path.Combine(artifacts_dir, $"language-package-{lng}.zip");
			using FileStream packageStream = new FileStream(lngPackFilePath, FileMode.Create);
			using ZipArchive lngPackFile = new ZipArchive(packageStream, ZipArchiveMode.Create);
			foreach (var lngArtifact in lngArtifacts)
			{
				Console.WriteLine($"add {lngArtifact} to language-package-{lng}.zip ...");
				var repoName = targetFiles.FirstOrDefault(f => f.OutputFile == lngArtifact).RepoName;
				using var fileStream = File.OpenRead(lngArtifact);
				using var artifactArchive = new ZipArchive(fileStream);
				var lngEntries = artifactArchive.Entries.Where(e => e.FullName.StartsWith($"{lng}/") && e.Length != 0);
				foreach (var entry in lngEntries)
				{
					var newEntryName = repoName + "/" + entry.FullName.Substring(lng.Length + 1);
					var newEntry = lngPackFile.CreateEntry(newEntryName);
					if (entry.Length != 0)
					{
						using var newEntryStream = newEntry.Open();
						using var stream = entry.Open();
						stream.CopyTo(newEntryStream);
					}
				}
			}
			packageStream.Flush();
			return Path.Combine(artifacts_dir, $"language-package-{lng}");
		}

		internal static void RemoveExclusivePlugins(string zipPath, string edition, List<ExclusiveConfig> exclusiveConfigs)
		{
			var exclusiveConfig = exclusiveConfigs.FirstOrDefault(e => e.Edition.Equals(edition, StringComparison.InvariantCultureIgnoreCase));
			if (exclusiveConfig == null || exclusiveConfig.ExclusivePlugins.Count == 0) return;

			using FileStream packageStream = new FileStream(zipPath, FileMode.Open);
			using ZipArchive lngPackFile = new ZipArchive(packageStream, ZipArchiveMode.Update);
			var exclusivePlugins = exclusiveConfig.ExclusivePlugins;

			foreach (var exclusivePlugin in exclusivePlugins)
			{
				var exclusiveEntries = lngPackFile.Entries.Where(e => e.FullName.StartsWith(exclusivePlugin)).ToList();
				for (int i = 0; i < exclusiveEntries.Count; i++)
				{
					exclusiveEntries[i].Delete();
				}
			}
		}

		internal static async Task<LngResourceConfigValidateRes> ValidateLanguageResources(List<string> lngArtifacts)
		{
			var res = new LngResourceConfigValidateRes();
			var resourceNameDict = new Dictionary<string, List<ResourceFile>>();
			foreach (var lngArtifact in lngArtifacts)
			{
				using var fileStream = File.OpenRead(lngArtifact);
				using var artifactArchive = new ZipArchive(fileStream);
				var lngResources = await ExtractResourceFilesAsync(artifactArchive);
				lngResources.ForEach(resourceItem =>
				{
					var resourceNames = new List<ResourceFile>();
					if (resourceNameDict.ContainsKey(resourceItem.Lng))
					{
						resourceNameDict.TryGetValue(resourceItem.Lng, out resourceNames);
					}
					else
					{
						resourceNameDict.TryAdd(resourceItem.Lng, resourceNames);
					}

					var validationRes = OrderAndObsoleteValidator.ValidateResourceFileOrderAndObsolete(resourceItem);
					if (validationRes.OrderDuplicatedItems.Count > 0
					|| validationRes.MultiOrdersItems.Count > 0
					|| validationRes.InvalidOrderValItems.Count > 0
					|| validationRes.MultiObsoleteItems.Count > 0)
					{
						res.Valid = false;
						if (validationRes.MultiOrdersItems.Count > 0)
						{
							if (res.MultiOrdersItems.TryGetValue(validationRes.Lng, out var multiOrderItems) && multiOrderItems != null)
							{
								multiOrderItems.AddRange(validationRes.MultiOrdersItems);
							}
							else
							{
								res.MultiOrdersItems.TryAdd(validationRes.Lng, validationRes.MultiOrdersItems);
							}
						}

						if (validationRes.InvalidOrderValItems.Count > 0)
						{
							if (res.InvalidOrderValueItems.TryGetValue(validationRes.Lng, out var orderNullItems) && orderNullItems != null)
							{
								orderNullItems.AddRange(validationRes.InvalidOrderValItems);
							}
							else
							{
								res.InvalidOrderValueItems.TryAdd(validationRes.Lng, validationRes.InvalidOrderValItems);
							}
						}

						if (validationRes.OrderDuplicatedItems.Count > 0)
						{
							if (res.OrderDuplicatedItems.TryGetValue(validationRes.Lng, out var orderDuplicatedItems) && orderDuplicatedItems != null)
							{
								orderDuplicatedItems.AddRange(validationRes.OrderDuplicatedItems);
							}
							else
							{
								res.OrderDuplicatedItems.TryAdd(validationRes.Lng, validationRes.OrderDuplicatedItems);
							}
						}

						if (validationRes.MultiObsoleteItems.Count > 0)
						{
							if (res.MultiObsoleteItems.TryGetValue(validationRes.Lng, out var multiOrderItems) && multiOrderItems != null)
							{
								multiOrderItems.AddRange(validationRes.MultiObsoleteItems);
							}
							else
							{
								res.MultiObsoleteItems.TryAdd(validationRes.Lng, validationRes.MultiObsoleteItems);
							}
						}
					}
					resourceNames.AddRange(resourceItem.Files);
				});
			}

			var duplicateItems = ModuleAndNameValidator.GetDulicatedModuleAndDisplayItems(resourceNameDict);
			if (duplicateItems.Count > 0)
			{
				res.Valid = false;
				res.ModuleAndNameDuplicatedItems = duplicateItems;
			}

			var invalidDisplayNameItems = DisplayNameValidator.GetInvalidDisplayItems(resourceNameDict);
			if (invalidDisplayNameItems.Count > 0)
			{
				res.Valid = false;
				res.InvalidDisplayNameItems = invalidDisplayNameItems;
			}
			return res;
		}

		private static async Task<List<ResourceItem>> ExtractResourceFilesAsync(ZipArchive zipArchive)
		{
			var res = new List<ResourceItem>();
			var customConfig = zipArchive.Entries.Where(e => e.Name == ResourceConfigEntryName).FirstOrDefault();
			using var resourceConfigStream = customConfig.Open();
			var resourceConfig = await JsonSerializer.DeserializeAsync<ResourceConfig>(resourceConfigStream,
				new JsonSerializerOptions()
				{
					ReadCommentHandling = JsonCommentHandling.Skip,
					AllowTrailingCommas = true,
					PropertyNameCaseInsensitive = true
				});
			res.AddRange(resourceConfig.Resources);
			return res;
		}
	}
}
