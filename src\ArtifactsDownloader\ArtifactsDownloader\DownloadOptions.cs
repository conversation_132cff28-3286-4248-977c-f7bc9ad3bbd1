﻿using CommandLine;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ArtifactsDownloader
{
	public class DownloadOptions
	{
		[Option('m', "Manifest file path", Required = true, HelpText = "Path to the manifest file")]
		public string ManifestFilePath { get; set; } = "";
		[Option('o', "Output directory", Required = true, HelpText = "Path to the output directory")]
		public string OutputDirectory { get; set; } = "";
		[Option('a', "Auto detect module version", Required = false, HelpText = "Auto detect module version")]
		public bool AutoDetectVersion { get; set; }
		[Option('v', "Version detect file path", Required = false, HelpText = "Path to the version detect file")]
		public string VersionDetectFilePath { get; set; } = "";
		[Option('e', "External dependencies package name", Required = false, HelpText = "Download the external dependencies package name")]
		public string ExternalDependenciesPackageName { get; set; } = "";
		[Option('c', "Connection string", Required = true, HelpText = "Connection string")]
		public string ConnectionString { get; set; } = "";

		[Option('r', "Root directory", Required = true, HelpText = "Root directory")]
		public string RootDirectory { get; set; } = "";	

    }
}
