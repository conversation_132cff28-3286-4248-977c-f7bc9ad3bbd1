#!/bin/bash
# $1 - wyn version
# $2 - source apt repository url
# $3 - source apt repository sas
# $4 - target apt repository url
# $5 - target apt repository sas

WynVersion=9.0
DebFileName=wynenterprise-$1.deb

#SourceRepoURL="https://wynintdelivery.blob.core.windows.net/wynintartifacts/installers/$WynVersion/$SpecificVersion"
#TargetRepoUrl="https://wyndelivery.blob.core.windows.net/wynartifacts/BI/beta/apt/$WynVersion"
SourceRepoURL=$2
SourceRepoSAS=$3
TargetRepoURL=$4
TargetRepoSAS=$5

GpgKeyPswd=kfhIoEufy08asG

# check parameter
echo "check parameters"
if [[ "$1" != "$WynVersion.00"* ]]; then
  echo "invalid version number $1 detected, the version number should be $WynVersion.00xxx.0"
  exit 1
fi

# copy DEB file to local
echo "copy dev DEB file to local"
rm -rf ./repo > /dev/null 2>&1
mkdir repo
cd repo
azcopy copy "$SourceRepoURL/$DebFileName?$SourceRepoSAS" ./
if [ ! -f ./$DebFileName ]; then
  echo "failed to copy dev DEB file to local"
  exit 1
fi

# configure apt repository
echo "configure apt repository"

azcopy copy "$TargetRepoURL/Packages?$TargetRepoSAS" ./Packages
if [ ! -f ./Packages ]; then
  echo "failed to copy Packages file to local"
  exit 1
fi

apt-ftparchive packages $DebFileName >> Packages
gzip -c Packages > Packages.gz
apt-ftparchive release . > Release
gpgconf --kill all
sleep 3
gpg --clear-sign --batch --pinentry-mode loopback --passphrase $GpgKeyPswd -o InRelease Release
gpg -abs -o Release.gpg Release

echo "push to azure storage"
azcopy copy $DebFileName "$TargetRepoURL/$DebFileName?$TargetRepoSAS"
azcopy copy InRelease "$TargetRepoURL/InRelease?$TargetRepoSAS"
azcopy copy Release "$TargetRepoURL/Release?$TargetRepoSAS"
azcopy copy Release.gpg "$TargetRepoURL/Release.gpg?$TargetRepoSAS"
azcopy copy Packages "$TargetRepoURL/Packages?$TargetRepoSAS"
azcopy copy Packages.gz "$TargetRepoURL/Packages.gz?$TargetRepoSAS"

# finish
echo "finished"
exit 0
