[{"Module": "調度模塊", "DisplayName": "定時任務", "Order": 1, "Obsolete": false, "Path": "gces-server/src/Gces.Scheduler.Plugin/clientApp/src/localization/tw.ts"}, {"Module": "調度模塊", "DisplayName": "儀表板任務", "Order": 2, "Obsolete": false, "Path": "gces-server/src/Gces.Scheduler.Plugin/clientApp/src/localization/dashboard/scheduling.zh_TW.ts"}, {"Module": "共享資源模塊", "DisplayName": "自定義地圖 1", "Order": 3, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/floorPlanDesigner/localization/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "自定義地圖 2", "Order": 4, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/floorPlanDesigner/localization/portalLocaleData/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "鑽取地圖 1", "Order": 5, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/geoJson/localization/tw.json"}, {"Module": "共享資源模塊", "DisplayName": "鑽取地圖 2", "Order": 6, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/geoJson/localization/portalLocaleData/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "文檔主題 1", "Order": 7, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/themeDesigner/localization/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "文檔主題 2", "Order": 8, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/themeDesigner/localization/portalLocaleData/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "文檔主題預覽", "Order": 9, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/themePreview/localization/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "可視化插件 1", "Order": 10, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/visual/localization/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "可視化插件 2", "Order": 11, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/visual/localization/portalLocaleData/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "網頁 1", "Order": 12, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/webPages/localization/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "網頁 2", "Order": 13, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/webPages/localization/portalLocaleData/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "字體 1", "Order": 14, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/font/localization/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "字體 2", "Order": 15, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/font/localization/portalLocaleData/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "語言資源 1", "Order": 16, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResource/localization/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "語言資源 2", "Order": 17, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResource/localization/portalLocaleData/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "語言資源預覽 1", "Order": 18, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResourcePreview/localization/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "語言資源預覽 2", "Order": 19, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResourcePreview/localization/portalLocaleData/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "活頁夾預覽 1", "Order": 20, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBindersPreview/localization/en.ts"}, {"Module": "共享資源模塊", "DisplayName": "活頁夾預覽 2", "Order": 21, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBindersPreview/localization/portalLocaleData/en.ts"}, {"Module": "門戶", "DisplayName": "錯誤頁面", "Order": 22, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Error/localization/tw.ts"}, {"Module": "管理門戶", "DisplayName": "依賴組件 1(Design Shell)", "Order": 23, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/AdminPortal/localization/designer-shell/tw.json"}, {"Module": "門戶", "DisplayName": "管理門戶", "Order": 24, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/AdminPortal/localization/tw.json"}, {"Module": "門戶", "DisplayName": "門戶公用信息", "Order": 25, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/tw.json"}, {"Module": "門戶", "DisplayName": "文檔分類", "Order": 26, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/TagsPanel/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "文檔類型", "Order": 27, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/ResourcePanel/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "文檔分類編輯器", "Order": 28, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/TagsEditor/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "工作區", "Order": 29, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/WorkspaceBase/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "上傳區域", "Order": 30, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/Upload/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "搜索（手機門戶）", "Order": 31, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/MobileSearch/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "資源門戶", "Order": 32, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/EN/ResourcePortal/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "文檔門戶 1", "Order": 33, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/EN/DocumentPortal/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "文檔門戶 2", "Order": 34, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/ZH/DocumentPortal/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "手機門戶", "Order": 35, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/ZH/MobilePortal/localization/tw.ts"}, {"Module": "管理門戶", "DisplayName": "系統診斷", "Order": 36, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/SystemDiagnostic/localization/tw.ts"}, {"Module": "管理門戶", "DisplayName": "節點管理", "Order": 37, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/NodeManagement/localization/tw.json"}, {"Module": "管理門戶", "DisplayName": "審計日誌", "Order": 38, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/AuditLogConfiguration/localization/tw.json"}, {"Module": "門戶", "DisplayName": "複製文檔鏈接", "Order": 39, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/CopyUrlDialog/localization/tw.ts"}, {"Module": "管理門戶", "DisplayName": "文檔權限編輯", "Order": 40, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/DocsPermissionEditor/localization/tw.json"}, {"Module": "管理門戶", "DisplayName": "文檔分類編輯", "Order": 41, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/DocumentsTagsEditor/localization/tw.ts"}, {"Module": "管理門戶", "DisplayName": "外部存儲", "Order": 42, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/ExternalStorage/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "私人分類", "Order": 43, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/PersonalTagManagement/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "個人配置", "Order": 44, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/ProfileEditor/localization/tw.ts"}, {"Module": "管理門戶", "DisplayName": "運行計劃模板", "Order": 45, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/Schedule/localization/tw.ts"}, {"Module": "管理門戶", "DisplayName": "系統管理", "Order": 46, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/SystemManagement/localization/tw.json"}, {"Module": "管理門戶", "DisplayName": "任務列表", "Order": 47, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/WorkerTiles/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "回收站", "Order": 48, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/SoftDeletedDocument/localization/tw.ts"}, {"Module": "數據監控", "DisplayName": "數據監控 1", "Order": 49, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DataMonitoring/localization/tw.ts"}, {"Module": "數據監控", "DisplayName": "數據監控 2", "Order": 50, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DataMonitoring/localization/editor/tw.json"}, {"Module": "門戶", "DisplayName": "文檔草稿 1", "Order": 51, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DocumentDraftList/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "文檔草稿 2", "Order": 52, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DocumentDraftPanel/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "文檔草稿 3", "Order": 53, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/PublishRequestList/localization/tw.ts"}, {"Module": "管理門戶", "DisplayName": "文件型數據源存儲", "Order": 54, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/FileDataSourceConfiguration/localization/tw.ts"}, {"Module": "文檔排序", "DisplayName": "文檔類型", "Order": 55, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/DocumentTypesLocaleData/tw.json"}, {"Module": "文檔排序", "DisplayName": "文檔拓展名", "Order": 56, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/DocumentExtLocaleData/tw.json"}, {"Module": "應用程序接口", "DisplayName": "公用錯誤(Server Plugin)", "Order": 57, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/ApiErrorsLocaleData/v2/tw.json"}, {"Module": "門戶", "DisplayName": "設計器工作表", "Order": 58, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/Designer/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "預覽標籤頁", "Order": 59, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/PreviewTabs/localization/tw.ts"}, {"Module": "門戶", "DisplayName": "文件活頁夾", "Order": 60, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBinder/localization/portalLocalData/tw.ts"}, {"Module": "共享資源模塊", "DisplayName": "文件活頁夾設計器", "Order": 61, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBinder/localization/tw.ts"}, {"Module": "認證模塊", "DisplayName": "許可證", "Order": 62, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/license/localization/tw.ts"}, {"Module": "認證模塊", "DisplayName": "登錄頁", "Order": 63, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/login/localization/tw.ts"}, {"Module": "認證模塊", "DisplayName": "登錄至組織", "Order": 64, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/login-to-tenant/localization/tw.ts"}, {"Module": "認證模塊", "DisplayName": "試用信息注冊", "Order": 65, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/trial-info-register/localization/tw.ts"}, {"Module": "認證模塊", "DisplayName": "驗證碼頁面", "Order": 66, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/verification/localization/tw.ts"}, {"Module": "應用程序接口", "DisplayName": "公用錯誤(Dataset Plugin)", "Order": 67, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "數據集類型", "Order": 68, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/documentSectionRegisterLocaleData/tw.ts"}, {"Module": "數據集", "DisplayName": "參數面板", "Order": 69, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/previewParameterRegisterLocaleData/tw.ts"}, {"Module": "數據集", "DisplayName": "消息彈窗", "Order": 70, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/MessageBox/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "提示膠囊", "Order": 71, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/notifications/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "推送數據集|流式數據集", "Order": 72, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/singleTableDataset/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "數據集緩存", "Order": 73, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/CotManagement/localization/tw.ts"}, {"Module": "數據源", "DisplayName": "數據源管理", "Order": 74, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DataProviders/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "緩存數據集", "Order": 75, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasetDesigner/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "流式數據集", "Order": 76, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/StreamingDatasetDesigner/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "推送數據集", "Order": 77, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/PushDatasetDesigner/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "原生查詢數據集 1", "Order": 78, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/NativeQueryDatasetDesigner/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "原生查詢數據集 2", "Order": 79, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/NativeQueryDataset/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "數據集側邊欄", "Order": 80, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasetSidebarTab/localization/tw.ts"}, {"Module": "數據源", "DisplayName": "數據源設計器", "Order": 81, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasourceDesigner/localization/tw.json"}, {"Module": "數據源", "DisplayName": "數據源側邊欄", "Order": 82, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasourceSidebarTab/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "數據集預覽", "Order": 83, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/PreviewDataset/localization/tw.ts"}, {"Module": "數據源", "DisplayName": "數據源預覽", "Order": 84, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/PreviewDatasource/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "數據集基本信息", "Order": 85, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/localization/portal.tw.json"}, {"Module": "數據集", "DisplayName": "準備數據 1", "Order": 86, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/creations/PrepareData/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "準備數據 2", "Order": 87, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/creations/PrepareData/localization/portalRegisterLocaleData/tw.ts"}, {"Module": "數據源", "DisplayName": "追加|覆蓋 數據(Excel數據源)", "Order": 88, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/ExcelData/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "增量更新緩存", "Order": 89, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/IncrementalUpdateCOT/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "刷新緩存", "Order": 90, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/RefreshCOT/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "推送數據(流式數據集)", "Order": 91, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/StreamingDatasetInsertDataDialog/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "推送與清除數據(推送數據集)", "Order": 92, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/PushDataset/localization/tw.ts"}, {"Module": "數據集", "DisplayName": "數據源導出", "Order": 93, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/Export/localization/tw.ts"}, {"Module": "數據模型", "DisplayName": "語義模型 1", "Order": 94, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/SemanticDesigner/localization/tw.ts"}, {"Module": "數據模型", "DisplayName": "語義模型 2", "Order": 95, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/SemanticDesigner/localization/dataset.tw.ts"}, {"Module": "數據模型", "DisplayName": "語義模型 3", "Order": 96, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/SemanticDesigner/localization/portalData.tw.ts"}, {"Module": "數據集", "DisplayName": "函數說明", "Order": 97, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/Resource/FnDescription-tw.json"}, {"Module": "數據集", "DisplayName": "SQL函數說明", "Order": 98, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/Resource/SqlFunction-tw.json"}, {"Module": "數據集", "DisplayName": "數據模塊磁力貼", "Order": 99, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/TilesRegister/localization/tw.ts"}, {"Module": "賬戶管理", "DisplayName": "賬戶管理模塊磁力貼", "Order": 100, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/localization/portalLocaleData/tw.js"}, {"Module": "賬戶管理", "DisplayName": "基礎信息 1", "Order": 101, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/localization/tw.js"}, {"Module": "賬戶管理", "DisplayName": "基礎信息 2", "Order": 102, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/Common/localization/tw.json"}, {"Module": "賬戶管理", "DisplayName": "角色管理", "Order": 103, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/RoleManagement/localization/tw.json"}, {"Module": "賬戶管理", "DisplayName": "組織管理", "Order": 104, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/TenantManagement/localization/tw.ts"}, {"Module": "賬戶管理", "DisplayName": "用戶管理", "Order": 105, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/UserManagement/localization/tw.ts"}, {"Module": "物聯網數據接入", "DisplayName": "物聯網數據接入", "Order": 106, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/IoTDataDesigner/localization/tw.json"}, {"Module": "賬戶管理", "DisplayName": "同步設置", "Order": 107, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/SynchronizationSetting/localization/tw.json"}, {"Module": "數據模型", "DisplayName": "抽取模型和直連查詢模型設計器錯誤", "Order": 108, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/localization/error.tw.json"}, {"Module": "數據模型", "DisplayName": "抽取模型和直連查詢模型設計器", "Order": 109, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/localization/model.tw.json"}, {"Module": "數據模型", "DisplayName": "抽取模型和直連查詢模型設計器2", "Order": 110, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/localization/wizard.tw.json"}, {"Module": "數據模型", "DisplayName": "抽取模型和直連查詢模型基礎信息", "Order": 111, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/Register/localization/portal.tw.ts"}, {"Module": "數據模型", "DisplayName": "抽取模型和直連查詢模型設計器消息彈窗", "Order": 112, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/Common/MessageBox/localization/tw.ts"}, {"Module": "數據模型", "DisplayName": "運行計劃", "Order": 113, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/TaskTab/localization/tw.ts"}, {"Module": "數據模型", "DisplayName": "重新抽取數據", "Order": 114, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/PortalReload/localization/tw.json"}, {"Module": "數據模型", "DisplayName": "抽取模型和直連查詢模型文檔列表", "Order": 115, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/PortalReload/localization/portalLocaleData/tw.ts"}, {"Module": "數據模型", "DisplayName": "數據模型緩存管理", "Order": 116, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/DataModelCacheManagement/localization/tw.ts"}, {"Module": "數據模型", "DisplayName": "API錯誤", "Order": 117, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/Common/localization/apiError/tw.json"}, {"Module": "儀表板", "DisplayName": "儀表板設置", "Order": 118, "Obsolete": false, "Path": "gces-dashboard/frontend/src/adminPortal/localization/zh_TW.json"}, {"Module": "儀表板", "DisplayName": "門戶1", "Order": 119, "Obsolete": false, "Path": "gces-dashboard/frontend/src/portalRegister/localization/portal/zh_TW.json"}, {"Module": "儀表板", "DisplayName": "門戶2", "Order": 120, "Obsolete": false, "Path": "gces-dashboard/frontend/src/portalRegister/localization/dashboard/zh_TW.json"}, {"Module": "儀表板", "DisplayName": "門戶3", "Order": 121, "Obsolete": false, "Path": "gces-dashboard/frontend/src/portalRegister/localization/scene/zh_TW.json"}, {"Module": "儀表板", "DisplayName": "3D場景", "Order": 122, "Obsolete": false, "Path": "gces-dashboard/frontend/packages/sceneCore/locales/scene/zh_TW.json"}, {"Module": "儀表板", "DisplayName": "儀表板", "Order": 123, "Obsolete": false, "Path": "gces-dashboard/frontend/packages/dashboardCore/locales/dashboard/zh_TW.json"}, {"Module": "儀表板", "DisplayName": "儀表板依賴", "Order": 124, "Obsolete": false, "Path": "gces-dashboard/frontend/packages/dashboardCore/locales/third/zh_TW.json"}, {"Module": "報表", "DisplayName": "系統管理 - 報表通用", "Order": 125, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/localization/zh-tw/common.json"}, {"Module": "報表", "DisplayName": "系統管理 - 報表提醒", "Order": 126, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/localization/zh-tw/reporting.json"}, {"Module": "報表", "DisplayName": "系統管理 - 系統設置  - 報表設置", "Order": 127, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/localization/zh-tw/portal.json"}, {"Module": "報表", "DisplayName": "門戶 - 報表通用", "Order": 128, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/common/localization/zh-tw/portal.json"}, {"Module": "報表", "DisplayName": "門戶 - 報表特性", "Order": 129, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/common/localization/zh-tw/reporting.json"}, {"Module": "報表", "DisplayName": "系統管理 - 報表導出模板", "Order": 130, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/ExportSettingTemplates/localization/tw.json"}, {"Module": "報表", "DisplayName": "系統管理 - 自定義函數", "Order": 131, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/UserFunctions/localization/tw.json"}, {"Module": "報表", "DisplayName": "系統管理 - 報表設置", "Order": 132, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/ReportingSettings/localization/tw.json"}, {"Module": "報表", "DisplayName": "門戶 - 報表枚舉", "Order": 133, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/common/localization/zh-tw/enums.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器 - 通用", "Order": 134, "Obsolete": false, "Path": "gces-reporting/gces-reporting-designer/src/i18n/wynReportDesigner/zh-TW.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器 - 數據集", "Order": 135, "Obsolete": false, "Path": "gces-reporting/gces-reporting-designer/src/i18n/wynReportDesignerDataSetSchemaErrors/zh-TW.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 裝飾項", "Order": 136, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-adorners.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 說明文字", "Order": 137, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-captions.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 圖表導航", "Order": 138, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-chartWizard.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 通用", "Order": 139, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-common.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 組件", "Order": 140, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-components.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 上下文操作", "Order": 141, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-contextActions.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 默認", "Order": 142, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-defaults.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 對話框", "Order": 143, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-dialogs.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 文檔API", "Order": 144, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-documentsAPI.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 枚舉", "Order": 145, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-enums.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 通知1", "Order": 146, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-error.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 表達式字段", "Order": 147, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-expressionFields.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 過濾", "Order": 148, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-filters.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 組編輯器", "Order": 149, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-groupEditor.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 標籤", "Order": 150, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-labels.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 間距尺寸", "Order": 151, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-marginsSizes.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 名稱模板", "Order": 152, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-nameTemplates.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 -  通知2", "Order": 153, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-notifications.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 頁面尺寸", "Order": 154, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-pageSizes.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 參數面板編輯器", "Order": 155, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-parametersViewEditor.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 屬性描述", "Order": 156, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-propertyDescriptors.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 屬性編輯器", "Order": 157, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-propertyEditors.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 報表條目", "Order": 158, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-reportItems.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 報表條目標籤", "Order": 159, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-romLabels.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 矩表嚮導", "Order": 160, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-tablixWizard.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 驗證", "Order": 161, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-validationErrors.json"}, {"Module": "報表設計器", "DisplayName": "報表設計器核心 - 通知3", "Order": 162, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh-TW/zh-TW-warning.json"}, {"Module": "報表查看器", "DisplayName": "查看器 - 通用", "Order": 163, "Obsolete": false, "Path": "gces-reporting/gces-reporting-viewer/src/i18n/zh-TW/wynReportViewer.json"}, {"Module": "報表查看器", "DisplayName": "查看器 - 核心", "Order": 164, "Obsolete": false, "Path": "gces-reporting/gces-reporting-viewer/src/i18n/zh-TW/viewer.json"}, {"Module": "報表查看器", "DisplayName": "查看器 - 通知", "Order": 165, "Obsolete": false, "Path": "gces-reporting/gces-reporting-viewer/src/i18n/zh-TW/wynReportViewerErrors.json"}, {"Module": "報表組件", "DisplayName": "參數面板 - 通用", "Order": 166, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/zh-tw/params.json"}, {"Module": "報表組件", "DisplayName": "參數面板 - 驗證", "Order": 167, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/zh-tw/paramsValidation.json"}, {"Module": "報表組件", "DisplayName": "參數面板 - 查看", "Order": 168, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/zh-tw/paramsView.json"}, {"Module": "報表組件", "DisplayName": "參數", "Order": 169, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/zh-tw/wynReportParameters.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - 枚舉", "Order": 170, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/enums.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - 通用", "Order": 171, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/common.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - 發送", "Order": 172, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/delivery.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - CSV", "Order": 173, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/export-csv.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - Word", "Order": 174, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/export-docx.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - Excel", "Order": 175, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/export-excel.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - Excel 數據", "Order": 176, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/export-excel-data.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - HTML", "Order": 177, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/export-html.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - Image", "Order": 178, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/export-image.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - JSON", "Order": 179, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/export-json.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - PDF", "Order": 180, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/export-pdf.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - TXT", "Order": 181, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/export-txt.json"}, {"Module": "報表組件", "DisplayName": "導出設置 - XML", "Order": 182, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh-tw/export-xml.json"}, {"Module": "報表實用程序", "DisplayName": "HTTP客戶端", "Order": 183, "Obsolete": false, "Path": "gces-reporting/gces-reporting-utils/src/httpClient/i18n/zh-tw.json"}, {"Module": "報表實用程序", "DisplayName": "報表語言", "Order": 184, "Obsolete": false, "Path": "gces-reporting/gces-reporting-utils/src/reportLanguages/i18n/zh-tw.json"}, {"Module": "報表實用程序", "DisplayName": "自訂參數視圖", "Order": 185, "Obsolete": false, "Path": "gces-reporting/gces-reporting-utils/src/parametersView/i18n/zh-tw.json"}, {"Module": "組件庫", "DisplayName": "文檔引用", "Order": 186, "Obsolete": false, "Path": "gces-js-common/packages/gces-references-component/src/localization/tw.ts"}, {"Module": "組件庫", "DisplayName": "共享", "Order": 187, "Obsolete": false, "Path": "gces-js-common/packages/wyn-components/src/localization/tw.ts"}, {"Module": "ETL", "DisplayName": "ETL設計器", "Order": 188, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-添加公式", "Order": 189, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/AddFormula/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-合併記錄", "Order": 190, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/AppendRecords/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-合併欄位", "Order": 191, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/CombineFields/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-過濾記錄", "Order": 192, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/FilterRecords/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-分組", "Order": 193, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/GroupBy/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-輸入源", "Order": 194, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/InputSource/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-連接數據", "Order": 195, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/JoinData/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-輸出目標", "Order": 196, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/OutputTarget/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-行列轉換", "Order": 197, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/Pivot/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-去除重複項", "Order": 198, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/RemoveDuplicates/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-選擇欄位", "Order": 199, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/SelectFields/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-設置欄位類型", "Order": 200, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/SetFieldType/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-拆分記錄", "Order": 201, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/SplitRecords/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-列行轉換", "Order": 202, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/Unpivot/localization/tw.json"}, {"Module": "ETL", "DisplayName": "命令-公共資源", "Order": 203, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/CommonLocalization/tw.json"}, {"Module": "儀表板", "DisplayName": "對話分析", "Order": 204, "Obsolete": false, "Path": "gces-dashboard/frontend/src/smartAnalyzer/i18n/locals/zh_TW.json"}, {"Module": "Dashboard", "DisplayName": "管理門戶", "Order": 205, "Obsolete": false, "Path": "gces-dashboard/frontend/src/adminPortal/common/localization/zh_TW.json"}, {"Module": "Reporting Components", "DisplayName": "Doc Permissions Tree", "Order": 206, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/PermissionsTree/i18n/zh-tw/permissionsTree.json"}, {"Module": "管理門戶", "DisplayName": "打印機管理", "Order": 207, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/Printer/localization/tw.json"}, {"Module": "數據模型", "DisplayName": "抽取模型和直連查詢模型設計器錯誤", "Order": 208, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/localization/error.tw.json"}, {"Module": "數據模型", "DisplayName": "抽取模型和直連查詢模型設計器", "Order": 209, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/localization/model.tw.json"}, {"Module": "數據模型", "DisplayName": "抽取模型和直連查詢模型設計器2", "Order": 210, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/localization/wizard.tw.json"}, {"Module": "數據模型", "DisplayName": "數據模型緩存管理", "Order": 211, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/CacheManagement/localization/tw.ts"}, {"Module": "數據模型", "DisplayName": "API錯誤", "Order": 212, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/Common/localization/apiError/tw.json"}, {"Module": "數據模型", "DisplayName": "數據模型預覽", "Order": 213, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisPreview/localization/tw.json"}, {"Module": "Dataset", "DisplayName": "AI設置", "Order": 214, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/common/AISettings/zh-TW.json"}]