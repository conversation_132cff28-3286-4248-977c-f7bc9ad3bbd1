name: Build Linux Installers

on:
  workflow_call:
    inputs:
      product_version:
        description: 'The product version'
        required: true
        type: string
      build_version:
        description: 'The build version'
        required: true
        type: string
      dev_artifacts_url:
        required: true
        type: string
      dev_docker_registry:
        required: true
        type: string
    secrets:
      dev_plugin_artifacts_sas_token:
        required: true
        type: string
      dev_artifacts_sas_token:
        required: true
        type: string
      dev_docker_username:
        required: true
        type: string
      dev_docker_password:
        required: true
        type: string

env:
  DEV_PLUGIN_ARTIFACTS_SAS_TOKEN: ${{ secrets.dev_plugin_artifacts_sas_token }}
  DEV_ARTIFACTS_URL: ${{ inputs.dev_artifacts_url }}
  DEV_ARTIFACTS_SAS_TOKEN: ${{ secrets.dev_artifacts_sas_token }}
  DEV_DOCKER_REGISTRY: ${{ inputs.dev_docker_registry }}
  DEV_DOCKER_USERNAME: ${{ secrets.dev_docker_username }}
  DEV_DOCKER_PASSWORD: ${{ secrets.dev_docker_password }}
  ARTIFACTS_DIR: "/var/tmp/wyn-build/artifacts"
  CONTENT_DIR: "/var/tmp/wyn-build/installation_content"
  PRODUCT_VERSION: ${{ inputs.product_version }}
  BUILD_VERSION: ${{ inputs.build_version }}

jobs:
  build-linux-installers:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          repository: wyn-core/gces-build
          ref: refs/heads/release/v${{ env.PRODUCT_VERSION }}-en

      - name: Setup NetCore SDK
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: |
            6.0.x
            8.0.x
          cache: true

      - name: Setup zip
        run: |
          apt-get update
          apt-get install -y apt-utils
          apt-get install -y zip

      - name: Download artifacts
        run: |
          mkdir -p $ARTIFACTS_DIR
          echo "downloading artifacts..."
          dotnet ./Tools/NewDownloader/ArtifactsDownloader.dll -m ./Release/manifest.txt -o $ARTIFACTS_DIR -v ./Release/version-detect.tx -e Dependencies_EN_Linux_$PRODUCT_VERSION -r plugin_artifacts -c https://wynintdelivery.blob.core.windows.net/wynintartifacts?$DEV_PLUGIN_ARTIFACTS_SAS_TOKEN

      - name: Build language packs
        run: |
          echo "building language packs..."
          dotnet ./Tools/Bundler/gces-language-package-bundler.dll ./Release/manifest.txt $ARTIFACTS_DIR $BUILD_VERSION True ./Release/resource-order

      - name: Prepare content
        run: |
          echo "preparing content..."
          mkdir -p $CONTENT_DIR
          bash ./prepare_content.sh $ARTIFACTS_DIR $CONTENT_DIR $BUILD_VERSION

      - name: Setup Docker Buildx
        run: |
          docker buildx ls | grep linux/arm64 > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "docker buildx is already setupped"
          else
            echo "setting up docker buildx..."
            apt-get update
            apt-get install -y qemu-user-static binfmt-support
            docker run --rm --privileged multiarch/qemu-user-static --reset -p yes
            docker buildx ls | grep linux/arm64
            if [ "0" == "$?" ]; then
              echo "docker buildx is setupped successfully"
            else
              echo "setup docker buildx failed"
              exit 1
            fi
          fi

      - name: Build x64 docker image
        run: |
          echo "building x64 docker image..."
          IMAGE_NAME=wyn-$PRODUCT_VERSION-en-dev
          cd ./EN/Docker
          bash build-and-push.sh $IMAGE_NAME $BUILD_VERSION $CONTENT_DIR $ARTIFACTS_DIR/dependencies/builtin_data $DEV_DOCKER_REGISTRY $DEV_DOCKER_USERNAME $DEV_DOCKER_PASSWORD
          docker manifest inspect $DEV_DOCKER_REGISTRY/$IMAGE_NAME:$BUILD_VERSION > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "build and push x64 docker image successfully"
            exit 0
          else
            echo "build and push x64 docker image failed"
            exit 1
          fi

      - name: Build arm64 docker image
        run: |
          echo "building arm64 docker image..."
          IMAGE_NAME=wyn-$PRODUCT_VERSION-en-arm64-dev
          cd ./EN/Docker
          bash build-and-push.sh $IMAGE_NAME $BUILD_VERSION $CONTENT_DIR $ARTIFACTS_DIR/dependencies/builtin_data $DEV_DOCKER_REGISTRY $DEV_DOCKER_USERNAME $DEV_DOCKER_PASSWORD --platform linux/arm64
          docker manifest inspect $DEV_DOCKER_REGISTRY/$IMAGE_NAME:$BUILD_VERSION > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "build and push arm64 docker image successfully"
            exit 0
          else
            echo "build and push arm64 docker image failed"
            exit 1
          fi

      - name: Build k8s docker image
        run: |
          echo "building k8s docker image..."
          IMAGE_NAME=wyn-$PRODUCT_VERSION-en-k8s-dev
          cd ./EN/Docker
          bash build-and-push.sh $IMAGE_NAME $BUILD_VERSION $CONTENT_DIR $ARTIFACTS_DIR/dependencies/builtin_data $DEV_DOCKER_REGISTRY $DEV_DOCKER_USERNAME $DEV_DOCKER_PASSWORD --platform linux/amd64,linux/arm64 --manifest
          docker manifest inspect $DEV_DOCKER_REGISTRY/$IMAGE_NAME:$BUILD_VERSION > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "build and push k8s docker image successfully"
            exit 0
          else
            echo "build and push k8s docker image failed"
            exit 1
          fi

      - name: Setup azcopy
        run: |
          azcopy -v > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "azcopy is already installed"
          else
            echo "installing azcopy..."
            cd /var/tmp
            curl -sL https://aka.ms/downloadazcopy-v10-linux | tar -xz
            cp ./azcopy_linux_amd64_*/azcopy /usr/bin/
            cd -
          fi

      - name: Build deb package
        run: |
          echo "building deb package..."
          cd ./EN/Linux/apt
          bash ./build-dev.sh $BUILD_VERSION $ARTIFACTS_DIR/dependencies/builtin_data $CONTENT_DIR "$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$BUILD_VERSION" "$DEV_ARTIFACTS_SAS_TOKEN"

      - name: Setup rpm build environment
        run: |
          rpmbuild --version > /dev/null 2>&1
          if [ "0" == "$?" ]; then
            echo "rpmbuild is already installed"
          else
            echo "setting up rpm build environment..."
            apt-get update
            apt-get install -y rpm
            mkdir -p /var/rpmbuild/{BUILD,BUILDROOT,RPMS/x86_64,SOURCES,SPECS,SRPMS,tmp}
            echo "%_topdir /var/rpmbuild" > ~/.rpmmacros
            echo "%_tmppath %{_topdir}/tmp" >> ~/.rpmmacros
          fi

      - name: Build rpm package
        run: |
          echo "building rpm package..."
          cd ./EN/Linux/yum
          bash ./build-dev.sh $BUILD_VERSION $ARTIFACTS_DIR/dependencies/builtin_data $CONTENT_DIR "$DEV_ARTIFACTS_URL/$PRODUCT_VERSION/$BUILD_VERSION" "$DEV_ARTIFACTS_SAS_TOKEN"


