* Thu Oct 04 2012 <PERSON><PERSON>vin Sa<PERSON>pute <psatpute AT redhat DOT com>
- Resolved "Glyphs with multiple unicode encodings inhibit subsetting" #851790
- Resolved #851791, #854601 and #851825
- Following GASP table version as per Liberation old version. (Anti-aliasing disabled)
- Added support for Serbian glyphs for wikipedia #657849
- In Monospace fonts, isFixedPitch bit set via script for getting it recognized as Monospace in putty.exe

* Fri Jul 06 2012 Pravin Satpute <psatpute AT redhat DOT com>
- Initial version of Liberation fonts based on croscore fonts version 1.21.0
- Converted TTF files into SFD files to be open source.
- Update Copyright and License file
- set fsType bit to 0, Installable Embedding is allowed.
- Absolute value in HHeadAscent/Descent values for maintaining Metric compatibility.

