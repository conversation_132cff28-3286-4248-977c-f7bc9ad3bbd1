#!/bin/bash

ARCH=amd64
IMG_VESRION=$1
ALWAYS_BUILD=$2
DOCKER_REGISTRY=gcescr.azurecr.io

echo "Pulling latest mcr.microsoft.com/dotnet/aspnet:8.0-jammy..."
docker pull mcr.microsoft.com/dotnet/aspnet:8.0-jammy | grep "Status: Image is up to date for"
if [ "0" == "$?" ]; then
  if [ "true" == "$ALWAYS_BUILD" ]; then
    echo "Always build is enabled, will build even if no new image found..."
  else
    echo "No new image found, skip building..."
    exit 0
  fi
fi

echo "Logging in to $DOCKER_REGISTRY..."
docker login -u $docker_account -p $docker_password $DOCKER_REGISTRY

echo "Building ..."

docker build --no-cache -f dockerfile-base-$ARCH -t gces-base-$ARCH-$IMG_VESRION:latest .
docker tag gces-base-$ARCH-$IMG_VESRION:latest $DOCKER_REGISTRY/gces-base-$ARCH-$IMG_VESRION:latest
docker push $DOCKER_REGISTRY/gces-base-$ARCH-$IMG_VESRION:latest

docker build --no-cache -f dockerfile-$ARCH -t gces-$ARCH-$IMG_VESRION:latest .
docker tag gces-$ARCH-$IMG_VESRION:latest $DOCKER_REGISTRY/gces-$ARCH-$IMG_VESRION:latest
docker push $DOCKER_REGISTRY/gces-$ARCH-$IMG_VESRION:latest

docker rmi $DOCKER_REGISTRY/gces-base-$ARCH-$IMG_VESRION:latest
docker rmi gces-base-$ARCH-$IMG_VESRION:latest

docker rmi $DOCKER_REGISTRY/gces-$ARCH-$IMG_VESRION:latest
docker rmi gces-$ARCH-$IMG_VESRION:latest

docker buildx build --no-cache --platform linux/arm64 -t gces-arm64-$IMG_VESRION:latest -o type=docker -f dockerfile-arm64 .
docker tag gces-arm64-$IMG_VESRION:latest $DOCKER_REGISTRY/gces-arm64-$IMG_VESRION:latest
docker push $DOCKER_REGISTRY/gces-arm64-$IMG_VESRION:latest

docker rmi $DOCKER_REGISTRY/gces-arm64-$IMG_VESRION:latest
docker rmi gces-arm64-$IMG_VESRION:latest

docker builder prune -af

docker rmi $(docker images -f "dangling=true" -q)

echo "Done."
exit 0
