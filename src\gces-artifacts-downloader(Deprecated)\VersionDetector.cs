﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace gces_artifacts_downloader
{
	public class VersionDetector
	{
		private readonly string _versionDetectFilePath;

		protected VersionDetector() { }

		public VersionDetector(string versionDetectFilePath)
		{
			_versionDetectFilePath = versionDetectFilePath;
		}

		public async Task<List<(string repoName, string artifactName)>> DetectTheLatestVersionsAsync()
		{
			var latestVersions = new List<(string, string)>();
			Console.WriteLine("Detecting the latest artifacts...");
			var repos = ReadRepos();
			foreach (var repo in repos)
			{
				var artifactName = await DetectTheLatestArtifactAsync(repo.repoName, repo.pattern);
				latestVersions.Add((repo.repoName, artifactName));
			}
			return latestVersions;
		}

		private List<(string repoName, string pattern)> ReadRepos()
		{
			var repos = File.ReadLines(_versionDetectFilePath, Encoding.UTF8)
				.Where(line => !string.IsNullOrEmpty(line))
				.Select(repoNameWithArtifact =>
				{
					var segs = repoNameWithArtifact.Split(new char[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries).Select(s => s.Trim()).ToList();
					return (repoName: segs[0], pattern: segs[1]);
				}).ToList();
			return repos;
		}

		private async Task<string> DetectTheLatestArtifactAsync(string repoName, string pattern)
		{
			var url = $"{Consts.DefaultRepositoryUri}/{repoName}/downloads";
			var artifactsCollection = await GetArtifactsCollectionAsync(url);
			while (null != artifactsCollection)
			{
				var latestArtifact = GetTheLatestArtifactName(artifactsCollection.values, pattern);
				if (!string.IsNullOrEmpty(latestArtifact))
				{
					return latestArtifact;
				}
				else
				{
					artifactsCollection = await GetArtifactsCollectionAsync(artifactsCollection.next);
				}
			}
			return null;
		}

		private async Task<ArtifactsCollection> GetArtifactsCollectionAsync(string url)
		{
			try
			{
				using (var client = new HttpClient())
				{
					client.DefaultRequestHeaders.Add("Authorization", Consts.DefaultAuthorization);
					using (var reqMsg = new HttpRequestMessage(HttpMethod.Get, url))
					{
						var resp = await client.SendAsync(reqMsg);
						var content = await resp.Content.ReadAsStringAsync();
						return JsonConvert.DeserializeObject<ArtifactsCollection>(content);
					}
				}
			}
			catch (Exception e)
			{
				Console.WriteLine(e);
				return null;
			}
		}

		private string GetTheLatestArtifactName(IEnumerable<ArtifactInfo> artifacts, string pattern)
		{
			string latestArtifactName = null;
			int latestVersion = 0;
			var reg = new Regex(pattern);
			foreach (var artifact in artifacts)
			{
				var match = reg.Match(artifact.name);
				if (match.Success)
				{
					var version = int.Parse(match.Groups[1].Value);
					if (version >= latestVersion)
					{
						latestVersion = version;
						latestArtifactName = artifact.name;
					}
				}
			}
			return latestArtifactName;
		}
	}
}
