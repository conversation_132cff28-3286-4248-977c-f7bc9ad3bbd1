FROM mcr.microsoft.com/dotnet/aspnet:8.0 as dotnet
FROM postgres:14.1 as postgres

ENV DB_PROVIDER Postgres
ENV DB_HOST localhost
ENV DB_PORT 5432
ENV DB_USER postgres
ENV DB_PASSWORD postgres

RUN apt-get update && \
    apt-get install -y wget && \
    apt-get install -y gnupg apt-transport-https && \
    wget --output-document=- https://www.monetdb.org/downloads/MonetDB-GPG-KEY | apt-key add -

RUN echo "deb https://dev.monetdb.org/downloads/deb/ bullseye-20240924150140504883781 monetdb" > /etc/apt/sources.list.d/monetdb.list && \
    echo "deb-src https://dev.monetdb.org/downloads/deb/ bullseye-20240924150140504883781 monetdb" >> /etc/apt/sources.list.d/monetdb.list && \
    apt-get update && \
    apt-get install -y libmonetdb-stream25=11.43.23 libmonetdb-client25=11.43.23 libmonetdb25=11.43.23 monetdb-client=11.43.23 monetdb5-server=11.43.23 monetdb5-sql=11.43.23

RUN mkdir -p /usr/share/fonts/liberation-fonts-ttf-2.00.1
COPY ./liberation-fonts-ttf-2.00.1 /usr/share/fonts/liberation-fonts-ttf-2.00.1

RUN mkdir -p /usr/share/dotnet
COPY --from=dotnet /usr/share/dotnet /usr/share/dotnet
RUN ln -s /usr/share/dotnet/dotnet /usr/bin

COPY ./init-db.sh /var/tmp/init-db.sh
COPY ./startup.sh /startup.sh

RUN mkdir -p /app
COPY ./jre /app/jre

EXPOSE 5432
EXPOSE 54321

ENTRYPOINT bash /startup.sh