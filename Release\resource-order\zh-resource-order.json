[{"Module": "调度模块", "DisplayName": "定时任务", "Order": 1, "Obsolete": false, "Path": "gces-server/src/Gces.Scheduler.Plugin/clientApp/src/localization/zh.ts"}, {"Module": "调度模块", "DisplayName": "仪表板任务", "Order": 2, "Obsolete": false, "Path": "gces-server/src/Gces.Scheduler.Plugin/clientApp/src/localization/dashboard/scheduling.zh.ts"}, {"Module": "共享资源模块", "DisplayName": "自定义地图 1", "Order": 3, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/floorPlanDesigner/localization/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "自定义地图 2", "Order": 4, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/floorPlanDesigner/localization/portalLocaleData/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "钻取地图 1", "Order": 5, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/geoJson/localization/zh.json"}, {"Module": "共享资源模块", "DisplayName": "钻取地图 2", "Order": 6, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/geoJson/localization/portalLocaleData/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "文档主题 1", "Order": 7, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/themeDesigner/localization/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "文档主题 2", "Order": 8, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/themeDesigner/localization/portalLocaleData/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "文档主题预览", "Order": 9, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/themePreview/localization/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "可视化插件 1", "Order": 10, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/visual/localization/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "可视化插件 2", "Order": 11, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/visual/localization/portalLocaleData/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "网页 1", "Order": 12, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/webPages/localization/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "网页 2", "Order": 13, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/webPages/localization/portalLocaleData/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "字体 1", "Order": 14, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/font/localization/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "字体 2", "Order": 15, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/font/localization/portalLocaleData/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "语言资源 1", "Order": 16, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResource/localization/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "语言资源 2", "Order": 17, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResource/localization/portalLocaleData/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "语言资源预览 1", "Order": 18, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResourcePreview/localization/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "语言资源预览 2", "Order": 19, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResourcePreview/localization/portalLocaleData/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "活页夹预览 1", "Order": 20, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBindersPreview/localization/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "活页夹预览 2", "Order": 21, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBindersPreview/localization/portalLocaleData/zh.ts"}, {"Module": "门户", "DisplayName": "错误页面", "Order": 22, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Error/localization/zh.ts"}, {"Module": "管理门户", "DisplayName": "依赖组件 1(Design Shell)", "Order": 23, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/AdminPortal/localization/designer-shell/zh.json"}, {"Module": "门户", "DisplayName": "管理门户", "Order": 24, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/AdminPortal/localization/zh.json"}, {"Module": "门户", "DisplayName": "门户公用信息", "Order": 25, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/zh.json"}, {"Module": "门户", "DisplayName": "文档分类", "Order": 26, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/TagsPanel/localization/zh.ts"}, {"Module": "门户", "DisplayName": "文档类型", "Order": 27, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/ResourcePanel/localization/zh.ts"}, {"Module": "门户", "DisplayName": "文档分类编辑器", "Order": 28, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/TagsEditor/localization/zh.ts"}, {"Module": "门户", "DisplayName": "工作区", "Order": 29, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/WorkspaceBase/localization/zh.ts"}, {"Module": "门户", "DisplayName": "上传区域", "Order": 30, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/Upload/localization/zh.ts"}, {"Module": "门户", "DisplayName": "搜索（手机门户）", "Order": 31, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/MobileSearch/localization/zh.ts"}, {"Module": "门户", "DisplayName": "资源门户", "Order": 32, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/EN/ResourcePortal/localization/zh.ts"}, {"Module": "门户", "DisplayName": "文档门户 1", "Order": 33, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/EN/DocumentPortal/localization/zh.ts"}, {"Module": "门户", "DisplayName": "文档门户 2", "Order": 34, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/ZH/DocumentPortal/localization/zh.ts"}, {"Module": "门户", "DisplayName": "手机门户", "Order": 35, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/ZH/MobilePortal/localization/zh.ts"}, {"Module": "管理门户", "DisplayName": "系统诊断", "Order": 36, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/SystemDiagnostic/localization/zh.ts"}, {"Module": "管理门户", "DisplayName": "节点管理", "Order": 37, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/NodeManagement/localization/zh.json"}, {"Module": "管理门户", "DisplayName": "审计日志", "Order": 38, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/AuditLogConfiguration/localization/zh.json"}, {"Module": "门户", "DisplayName": "复制文档链接", "Order": 39, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/CopyUrlDialog/localization/zh.ts"}, {"Module": "管理门户", "DisplayName": "文档权限编辑", "Order": 40, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/DocsPermissionEditor/localization/zh.json"}, {"Module": "管理门户", "DisplayName": "文档分类编辑", "Order": 41, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/DocumentsTagsEditor/localization/zh.ts"}, {"Module": "管理门户", "DisplayName": "外部存储", "Order": 42, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/ExternalStorage/localization/zh.ts"}, {"Module": "门户", "DisplayName": "私人分类", "Order": 43, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/PersonalTagManagement/localization/zh.ts"}, {"Module": "门户", "DisplayName": "个人配置", "Order": 44, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/ProfileEditor/localization/zh.ts"}, {"Module": "管理门户", "DisplayName": "运行计划模板", "Order": 45, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/Schedule/localization/zh.ts"}, {"Module": "管理门户", "DisplayName": "系统管理", "Order": 46, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/SystemManagement/localization/zh.json"}, {"Module": "管理门户", "DisplayName": "任务列表", "Order": 47, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/WorkerTiles/localization/zh.ts"}, {"Module": "门户", "DisplayName": "回收站", "Order": 48, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/SoftDeletedDocument/localization/zh.ts"}, {"Module": "数据监控", "DisplayName": "数据监控 1", "Order": 49, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DataMonitoring/localization/zh.ts"}, {"Module": "数据监控", "DisplayName": "数据监控 2", "Order": 50, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DataMonitoring/localization/editor/zh.json"}, {"Module": "门户", "DisplayName": "文档草稿 1", "Order": 51, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DocumentDraftList/localization/zh.ts"}, {"Module": "门户", "DisplayName": "文档草稿 2", "Order": 52, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DocumentDraftPanel/localization/zh.ts"}, {"Module": "门户", "DisplayName": "文档草稿 3", "Order": 53, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/PublishRequestList/localization/zh.ts"}, {"Module": "管理门户", "DisplayName": "文件型数据源存储", "Order": 54, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/FileDataSourceConfiguration/localization/zh.ts"}, {"Module": "文档排序", "DisplayName": "文档类型", "Order": 55, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/DocumentTypesLocaleData/zh.json"}, {"Module": "文档排序", "DisplayName": "文档拓展名", "Order": 56, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/DocumentExtLocaleData/zh.json"}, {"Module": "应用程序接口", "DisplayName": "公用错误(Server Plugin)", "Order": 57, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/ApiErrorsLocaleData/v2/zh.json"}, {"Module": "门户", "DisplayName": "设计器工作表", "Order": 58, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/Designer/localization/zh.ts"}, {"Module": "门户", "DisplayName": "预览标签页", "Order": 59, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/PreviewTabs/localization/zh.ts"}, {"Module": "门户", "DisplayName": "文件活页夹", "Order": 60, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBinder/localization/portalLocalData/zh.ts"}, {"Module": "共享资源模块", "DisplayName": "文件活页夹设计器", "Order": 61, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBinder/localization/zh.ts"}, {"Module": "认证模块", "DisplayName": "许可证", "Order": 62, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/license/localization/zh.ts"}, {"Module": "认证模块", "DisplayName": "登录页", "Order": 63, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/login/localization/zh.ts"}, {"Module": "认证模块", "DisplayName": "登录至组织", "Order": 64, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/login-to-tenant/localization/zh.ts"}, {"Module": "认证模块", "DisplayName": "试用信息注册", "Order": 65, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/trial-info-register/localization/zh.ts"}, {"Module": "认证模块", "DisplayName": "验证码页面", "Order": 66, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/verification/localization/zh.ts"}, {"Module": "应用程序接口", "DisplayName": "公用错误(Dataset Plugin)", "Order": 67, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "数据集类型", "Order": 68, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/documentSectionRegisterLocaleData/zh.ts"}, {"Module": "数据集", "DisplayName": "参数面板", "Order": 69, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/previewParameterRegisterLocaleData/zh.ts"}, {"Module": "数据集", "DisplayName": "消息弹窗", "Order": 70, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/MessageBox/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "提示胶囊", "Order": 71, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/notifications/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "推送数据集|流式数据集", "Order": 72, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/singleTableDataset/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "数据集缓存", "Order": 73, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/CotManagement/localization/zh.ts"}, {"Module": "数据源", "DisplayName": "数据源管理", "Order": 74, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DataProviders/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "缓存数据集", "Order": 75, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasetDesigner/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "流式数据集", "Order": 76, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/StreamingDatasetDesigner/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "推送数据集", "Order": 77, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/PushDatasetDesigner/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "原生查询数据集 1", "Order": 78, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/NativeQueryDatasetDesigner/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "原生查询数据集 2", "Order": 79, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/NativeQueryDataset/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "数据集侧边栏", "Order": 80, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasetSidebarTab/localization/zh.ts"}, {"Module": "数据源", "DisplayName": "数据源设计器", "Order": 81, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasourceDesigner/localization/zh.json"}, {"Module": "数据源", "DisplayName": "数据源侧边栏", "Order": 82, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasourceSidebarTab/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "数据集预览", "Order": 83, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/PreviewDataset/localization/zh.ts"}, {"Module": "数据源", "DisplayName": "数据源预览", "Order": 84, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/PreviewDatasource/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "数据集基本信息", "Order": 85, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/localization/portal.zh.json"}, {"Module": "数据集", "DisplayName": "准备数据 1", "Order": 86, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/creations/PrepareData/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "准备数据 2", "Order": 87, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/creations/PrepareData/localization/portalRegisterLocaleData/zh.ts"}, {"Module": "数据源", "DisplayName": "追加|覆盖 数据(Excel数据源)", "Order": 88, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/ExcelData/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "增量更新缓存", "Order": 89, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/IncrementalUpdateCOT/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "刷新缓存", "Order": 90, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/RefreshCOT/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "推送数据(流式数据集)", "Order": 91, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/StreamingDatasetInsertDataDialog/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "推送与清除数据(推送数据集)", "Order": 92, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/PushDataset/localization/zh.ts"}, {"Module": "数据集", "DisplayName": "数据源导出", "Order": 93, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/Export/localization/zh.ts"}, {"Module": "数据模型", "DisplayName": "语义模型 1", "Order": 94, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/SemanticDesigner/localization/zh.ts"}, {"Module": "数据模型", "DisplayName": "语义模型 2", "Order": 95, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/SemanticDesigner/localization/dataset.zh.ts"}, {"Module": "数据模型", "DisplayName": "语义模型 3", "Order": 96, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/SemanticDesigner/localization/portalData.zh.ts"}, {"Module": "数据集", "DisplayName": "函数说明", "Order": 97, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/Resource/FnDescription-cn.json"}, {"Module": "数据集", "DisplayName": "SQL函数说明", "Order": 98, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/Resource/SqlFunction-cn.json"}, {"Module": "数据集", "DisplayName": "数据模块磁力贴", "Order": 99, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/TilesRegister/localization/zh.ts"}, {"Module": "账户管理", "DisplayName": "账户管理模块磁力贴", "Order": 100, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/localization/portalLocaleData/zh.js"}, {"Module": "账户管理", "DisplayName": "基础信息 1", "Order": 101, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/localization/zh.js"}, {"Module": "账户管理", "DisplayName": "基础信息 2", "Order": 102, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/Common/localization/zh.json"}, {"Module": "账户管理", "DisplayName": "角色管理", "Order": 103, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/RoleManagement/localization/zh.json"}, {"Module": "账户管理", "DisplayName": "组织管理", "Order": 104, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/TenantManagement/localization/zh.ts"}, {"Module": "账户管理", "DisplayName": "用户管理", "Order": 105, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/UserManagement/localization/zh.ts"}, {"Module": "物联网数据接入", "DisplayName": "物联网数据接入", "Order": 106, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/IoTDataDesigner/localization/zh.json"}, {"Module": "账户管理", "DisplayName": "同步设置", "Order": 107, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/SynchronizationSetting/localization/zh.json"}, {"Module": "数据模型", "DisplayName": "抽取模型和直连查询模型设计器错误", "Order": 108, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/localization/error.zh.json"}, {"Module": "数据模型", "DisplayName": "抽取模型和直连查询模型设计器", "Order": 109, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/localization/model.zh.json"}, {"Module": "数据模型", "DisplayName": "抽取模型和直连查询模型设计器2", "Order": 110, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/localization/wizard.zh.json"}, {"Module": "数据模型", "DisplayName": "抽取模型和直连查询模型基础信息", "Order": 111, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/Register/localization/portal.zh.ts"}, {"Module": "数据模型", "DisplayName": "抽取模型和直连查询模型设计器消息弹窗", "Order": 112, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/Common/MessageBox/localization/zh.ts"}, {"Module": "数据模型", "DisplayName": "运行计划", "Order": 113, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/TaskTab/localization/zh.ts"}, {"Module": "数据模型", "DisplayName": "重新抽取数据", "Order": 114, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/PortalReload/localization/zh.json"}, {"Module": "数据模型", "DisplayName": "抽取模型和直连查询模型文档列表", "Order": 115, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/PortalReload/localization/portalLocaleData/zh.ts"}, {"Module": "数据模型", "DisplayName": "数据模型缓存管理", "Order": 116, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/DataModelCacheManagement/localization/zh.ts"}, {"Module": "数据模型", "DisplayName": "API错误", "Order": 117, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/Common/localization/apiError/zh.json"}, {"Module": "仪表板", "DisplayName": "仪表板设置", "Order": 118, "Obsolete": false, "Path": "gces-dashboard/frontend/src/adminPortal/localization/zh.json"}, {"Module": "仪表板", "DisplayName": "门户1", "Order": 119, "Obsolete": false, "Path": "gces-dashboard/frontend/src/portalRegister/localization/portal/zh.json"}, {"Module": "仪表板", "DisplayName": "门户2", "Order": 120, "Obsolete": false, "Path": "gces-dashboard/frontend/src/portalRegister/localization/dashboard/zh.json"}, {"Module": "仪表板", "DisplayName": "门户3", "Order": 121, "Obsolete": false, "Path": "gces-dashboard/frontend/src/portalRegister/localization/scene/zh.json"}, {"Module": "仪表板", "DisplayName": "3D场景", "Order": 122, "Obsolete": false, "Path": "gces-dashboard/frontend/packages/sceneCore/locales/scene/zh.json"}, {"Module": "仪表板", "DisplayName": "仪表板", "Order": 123, "Obsolete": false, "Path": "gces-dashboard/frontend/packages/dashboardCore/locales/dashboard/zh.json"}, {"Module": "仪表板", "DisplayName": "仪表板依赖", "Order": 124, "Obsolete": false, "Path": "gces-dashboard/frontend/packages/dashboardCore/locales/third/zh.json"}, {"Module": "报表", "DisplayName": "系统管理 - 报表通用", "Order": 125, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/localization/zh/common.json"}, {"Module": "报表", "DisplayName": "系统管理 - 报表提醒", "Order": 126, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/localization/zh/reporting.json"}, {"Module": "报表", "DisplayName": "系统管理 - 系统设置 - 报表设置", "Order": 127, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/localization/zh/portal.json"}, {"Module": "报表", "DisplayName": "门户 - 报表通用", "Order": 128, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/common/localization/zh/portal.json"}, {"Module": "报表", "DisplayName": "门户 - 报表特性", "Order": 129, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/common/localization/zh/reporting.json"}, {"Module": "报表", "DisplayName": "系统管理 - 报表导出模板", "Order": 130, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/ExportSettingTemplates/localization/zh.json"}, {"Module": "报表", "DisplayName": "系统管理 - 自定义函数", "Order": 131, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/UserFunctions/localization/zh.json"}, {"Module": "报表", "DisplayName": "系统管理 - 报表设置", "Order": 132, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/ReportingSettings/localization/zh.json"}, {"Module": "报表", "DisplayName": "门户 - 报表枚举", "Order": 133, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/common/localization/zh/enums.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器 - 通用", "Order": 134, "Obsolete": false, "Path": "gces-reporting/gces-reporting-designer/src/i18n/wynReportDesigner/zh.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器 - 数据集", "Order": 135, "Obsolete": false, "Path": "gces-reporting/gces-reporting-designer/src/i18n/wynReportDesignerDataSetSchemaErrors/zh.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 装饰项", "Order": 136, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-adorners.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 说明文字", "Order": 137, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-captions.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 图表导航", "Order": 138, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-chartWizard.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 通用", "Order": 139, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-common.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 组件", "Order": 140, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-components.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 上下文操作", "Order": 141, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-contextActions.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 默认", "Order": 142, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-defaults.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 对话框", "Order": 143, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-dialogs.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 文档API", "Order": 144, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-documentsAPI.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 枚举", "Order": 145, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-enums.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 通知1", "Order": 146, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-error.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 表达式字段", "Order": 147, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-expressionFields.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 过滤", "Order": 148, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-filters.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 组编辑器", "Order": 149, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-groupEditor.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 标签", "Order": 150, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-labels.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 间距尺寸", "Order": 151, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-marginsSizes.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 名称模板", "Order": 152, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-nameTemplates.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 通知2", "Order": 153, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-notifications.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 页面尺寸", "Order": 154, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-pageSizes.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 参数面板编辑器", "Order": 155, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-parametersViewEditor.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 属性描述", "Order": 156, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-propertyDescriptors.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 属性编辑器", "Order": 157, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-propertyEditors.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 报表条目", "Order": 158, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-reportItems.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 报表条目标签", "Order": 159, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-romLabels.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 矩表向导", "Order": 160, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-tablixWizard.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 验证", "Order": 161, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-validationErrors.json"}, {"Module": "报表设计器", "DisplayName": "报表设计器核心 - 通知3", "Order": 162, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/zh/zh-warning.json"}, {"Module": "报表查看器", "DisplayName": "查看器 - 通用", "Order": 163, "Obsolete": false, "Path": "gces-reporting/gces-reporting-viewer/src/i18n/zh/wynReportViewer.json"}, {"Module": "报表查看器", "DisplayName": "查看器 - 核心", "Order": 164, "Obsolete": false, "Path": "gces-reporting/gces-reporting-viewer/src/i18n/zh/viewer.json"}, {"Module": "报表查看器", "DisplayName": "查看器 - 通知", "Order": 165, "Obsolete": false, "Path": "gces-reporting/gces-reporting-viewer/src/i18n/zh/wynReportViewerErrors.json"}, {"Module": "报表组件", "DisplayName": "参数面板 - 通用", "Order": 166, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/zh/params.json"}, {"Module": "报表组件", "DisplayName": "参数面板 - 验证", "Order": 167, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/zh/paramsValidation.json"}, {"Module": "报表组件", "DisplayName": "参数面板 - 查看", "Order": 168, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/zh/paramsView.json"}, {"Module": "报表组件", "DisplayName": "参数", "Order": 169, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/zh/wynReportParameters.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - 枚举", "Order": 170, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/enums.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - 通用", "Order": 171, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/common.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - 发送", "Order": 172, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/delivery.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - CSV", "Order": 173, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/export-csv.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - Word", "Order": 174, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/export-docx.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - Excel", "Order": 175, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/export-excel.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - Excel(数据)", "Order": 176, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/export-excel-data.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - HTML", "Order": 177, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/export-html.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - Image", "Order": 178, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/export-image.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - JSON", "Order": 179, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/export-json.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - PDF", "Order": 180, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/export-pdf.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - TXT", "Order": 181, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/export-txt.json"}, {"Module": "报表组件", "DisplayName": "导出设置 - XML", "Order": 182, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/zh/export-xml.json"}, {"Module": "报表实用程序", "DisplayName": "HTTP客户端", "Order": 183, "Obsolete": false, "Path": "gces-reporting/gces-reporting-utils/src/httpClient/i18n/zh.json"}, {"Module": "报表实用程序", "DisplayName": "报表语言", "Order": 184, "Obsolete": false, "Path": "gces-reporting/gces-reporting-utils/src/reportLanguages/i18n/zh.json"}, {"Module": "报表实用程序", "DisplayName": "自定义参数视图", "Order": 185, "Obsolete": false, "Path": "gces-reporting/gces-reporting-utils/src/parametersView/i18n/zh.json"}, {"Module": "组件库", "DisplayName": "文档引用", "Order": 186, "Obsolete": false, "Path": "gces-js-common/packages/gces-references-component/src/localization/zh.ts"}, {"Module": "组件库", "DisplayName": "共享", "Order": 187, "Obsolete": false, "Path": "gces-js-common/packages/wyn-components/src/localization/zh.ts"}, {"Module": "ETL", "DisplayName": "ETL设计器", "Order": 188, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-添加公式", "Order": 189, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/AddFormula/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-合并记录", "Order": 190, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/AppendRecords/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-合并字段", "Order": 191, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/CombineFields/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-过滤记录", "Order": 192, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/FilterRecords/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-分组", "Order": 193, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/GroupBy/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-输入", "Order": 194, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/InputSource/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-连接数据", "Order": 195, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/JoinData/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-输出", "Order": 196, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/OutputTarget/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-行列转换", "Order": 197, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/Pivot/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-去除重复项", "Order": 198, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/RemoveDuplicates/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-选择字段", "Order": 199, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/SelectFields/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-设置字段类型", "Order": 200, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/SetFieldType/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-拆分记录", "Order": 201, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/SplitRecords/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-列行转换", "Order": 202, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/Unpivot/localization/zh.json"}, {"Module": "ETL", "DisplayName": "命令-公共资源", "Order": 203, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/CommonLocalization/zh.json"}, {"Module": "仪表板", "DisplayName": "对话分析", "Order": 204, "Obsolete": false, "Path": "gces-dashboard/frontend/src/smartAnalyzer/i18n/locals/zh.json"}, {"Module": "Dashboard", "DisplayName": "管理门户", "Order": 205, "Obsolete": false, "Path": "gces-dashboard/frontend/src/adminPortal/common/localization/zh.json"}, {"Module": "Reporting Components", "DisplayName": "Doc Permissions Tree", "Order": 206, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/PermissionsTree/i18n/zh/permissionsTree.json"}, {"Module": "管理门户", "DisplayName": "打印机管理", "Order": 207, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/Printer/localization/zh.json"}, {"Module": "数据模型", "DisplayName": "抽取模型和直连查询模型设计器错误", "Order": 208, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/localization/error.zh.json"}, {"Module": "数据模型", "DisplayName": "抽取模型和直连查询模型设计器", "Order": 209, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/localization/model.zh.json"}, {"Module": "数据模型", "DisplayName": "抽取模型和直连查询模型设计器2", "Order": 210, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/localization/wizard.zh.json"}, {"Module": "数据模型", "DisplayName": "数据模型缓存管理", "Order": 211, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/CacheManagement/localization/zh.ts"}, {"Module": "数据模型", "DisplayName": "API错误", "Order": 212, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/Common/localization/apiError/zh.json"}, {"Module": "数据模型", "DisplayName": "数据模型预览", "Order": 213, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisPreview/localization/zh.json"}, {"Module": "Dataset", "DisplayName": "AI设置", "Order": 214, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/common/AISettings/zh.json"}]