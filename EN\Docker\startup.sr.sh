#!/bin/bash

if [ -d /app/Server/wwwroot/builtin-map ]; then
  mkdir -p /app/Server/wwwroot/map
  cp -r /app/Server/wwwroot/builtin-map/* /app/Server/wwwroot/map/
  rm -rf /app/Server/wwwroot/builtin-map
fi
if [ -d /app/Server/builtin-sps ]; then
  mkdir -p /app/Server/SecurityProviders
  cp -r /app/Server/builtin-sps/* /app/Server/SecurityProviders/
  rm -rf /app/Server/builtin-sps
fi

cd /app/ServiceRunner
exec dotnet ServiceRunner.dll
