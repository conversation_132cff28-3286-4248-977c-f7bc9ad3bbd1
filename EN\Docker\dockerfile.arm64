FROM gcescr.azurecr.io/gces-arm64-8.0

ENV DEBIAN_FRONTEND=noninteractive
ENV DB_PROVIDER=SQLite
ENV DB_HOST=""
ENV DB_PORT=""
ENV DB_USER=""
ENV DB_PASSWORD=""
ENV SINGLE_DATABASE_MODE=false
ENV DB_NAME_WYN=wyn
ENV DB_NAME_SERVERDATA=wynserverdata
ENV DB_NAME_DATACACHE=wyndatacache
ENV DB_NAME_IDENTITYSERVICE=wynis

ENV IMPORT_SAMPLES=true
ENV REQUIRE_HTTPS=false
ENV SINGLE_PROCESS_MODE=true
ENV DEFAULT_LANGUAGE=English

RUN mkdir -p /usr/share/fonts/liberation-fonts-ttf-2.00.1 && \
    mkdir -p /usr/local/share/ca-certificates/wyn

ADD --chmod=777 ./wyn ./wyn
COPY ./startup.arm64.sh /
COPY ./liberation-fonts-ttf-2.00.1 /usr/share/fonts/liberation-fonts-ttf-2.00.1
COPY ./jre /wyn/jre

EXPOSE 51980 5432 443

CMD ["/bin/bash", "/startup.arm64.sh"]
