﻿// gces-artifacts-downloader, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// gces_artifacts_downloader.ArtifactsDownloader
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using System.Diagnostics;
using System.Text;

namespace ArtifactsDownloader
{
	public class Downloader
	{
		private BlobContainerClient _containerClient;

		private DownloadOptions _options;

		public Downloader(DownloadOptions options)
		{
			_options = options;
			_containerClient = new BlobContainerClient(new Uri(options.ConnectionString));
			if (!File.Exists(_options.ManifestFilePath))
			{
				File.Create(_options.ManifestFilePath);
			}

			if (!Directory.Exists(_options.OutputDirectory))
			{
				Directory.CreateDirectory(_options.OutputDirectory);
			}

			if (!Directory.Exists(Path.Combine(_options.OutputDirectory, "dependencies")))
			{
				Directory.CreateDirectory(Path.Combine(_options.OutputDirectory, "dependencies"));
			}
		}

		public List<(string repoName, string version, string artifactName)> GetNeedDownloadedArtifacts()
		{
			List<string> downloadedFiles = new List<string>();
			List<(string, string, string)> targetFiles = (from line in File.ReadLines(_options.ManifestFilePath, Encoding.UTF8)
														  where !string.IsNullOrEmpty(line) && !line.Trim().StartsWith("//") && !line.Trim().StartsWith("#")
														  select line).Select(delegate (string repoNameWithArtifact)
														  {
															  List<string> list = (from s in repoNameWithArtifact.Split(' ', '\t', '\n')
																				   where !string.IsNullOrEmpty(s)
																				   select s.Trim()).ToList();
															  string item = list[0];
															  string item2 = list[1];
															  string text = list[2];
															  string path = Path.Combine(_options.OutputDirectory, text);
															  if (File.Exists(path))
															  {
																  downloadedFiles.Add(text);
															  }
															  return (item, item2, text);
														  }).ToList();
			string[] files = Directory.GetFiles(_options.OutputDirectory);
			string[] array = files;
			foreach (string file in array)
			{
				string fileName = Path.GetFileName(file);
				if (!targetFiles.Any<(string, string, string)>(((string RepoName, string Version, string OutputFile) f) => f.OutputFile.Equals(fileName, StringComparison.OrdinalIgnoreCase)))
				{
					File.Delete(file);
				}
			}
			return targetFiles.Where<(string, string, string)>(((string RepoName, string Version, string OutputFile) f) => !downloadedFiles.Any((string d) => d.Equals(f.OutputFile, StringComparison.OrdinalIgnoreCase))).ToList();
		}

		public bool Download()
		{
			if (!string.IsNullOrEmpty(_options.ExternalDependenciesPackageName) && !DownloadExternalDependencies())
			{
				return false;
			}

			List<(string, string, string)> tups = GetNeedDownloadedArtifacts();
			if (!tups.Any())
			{
				Console.WriteLine("No artifacts need to download.");
			}

			if (!DownloadArtifacts(tups))
			{
				return false;
			}

			return true;

		}

		private bool DownloadExternalDependencies()
		{
			try
			{
				var prefix = _options.RootDirectory+"/external-dependencies/" + _options.ExternalDependenciesPackageName;
				var objs = _containerClient.GetBlobs(BlobTraits.None, BlobStates.None, prefix);

				if (objs.Count() == 0)
				{
					Console.WriteLine($"The external dependencies package {_options.ExternalDependenciesPackageName} doesn't exist.");
					return false;
				}

				var remoteFileKey = objs.First().Name;
				var remoteFileName = Path.GetFileName(remoteFileKey);

				var externalDependenciesFolder = Path.Combine(_options.OutputDirectory, "dependencies");

				if (File.Exists(Path.Combine(externalDependenciesFolder, remoteFileName)))
				{
					Console.WriteLine($"The external dependencies package {_options.ExternalDependenciesPackageName} already exists.");
					return true;
				}
				else
				{
					//Remove the old version of the external dependencies package
					var localFiles = Directory.GetFiles(externalDependenciesFolder, _options.ExternalDependenciesPackageName + "*").ToList();
					if (localFiles.Count() > 0)
					{
						foreach (var file in localFiles)
						{
							File.Delete(file);
						}

						if (Directory.Exists(Path.Combine(externalDependenciesFolder, "builtin_data")))
						{
							Directory.Delete(Path.Combine(externalDependenciesFolder, "builtin_data"), true);
						}
					}

					var blob = _containerClient.GetBlobClient(remoteFileKey);
					string path = Path.Combine(externalDependenciesFolder, remoteFileName);
					using (var fileStream = File.OpenWrite(path))
					{
						blob.DownloadTo(fileStream);
					}
					Console.WriteLine($"Downloaded external dependencies package {remoteFileName}.");
				}
			}
			catch (Exception e)
			{
				Console.WriteLine($"Downloading external dependencies package {_options.ExternalDependenciesPackageName} failed.{Environment.NewLine}{e}");
				return false;
			}

			return true;
		}

		private bool DownloadArtifacts(IEnumerable<(string repoName, string version, string artifactName)> tups)
		{
			List<Task<bool>> tasks = new List<Task<bool>>();
			foreach (var tup in tups)
			{
				Task<bool> task = Task.Run(delegate
				{
					Stopwatch stopwatch = Stopwatch.StartNew();
					Console.WriteLine("Downloading " + tup.artifactName + " ...");
					try
					{
						var blob = _containerClient.GetBlobClient(_options.RootDirectory + "/" + tup.repoName + "/" + tup.version + "/" + tup.artifactName);
						string path = Path.Combine(_options.OutputDirectory, tup.artifactName);

						using (var fileStream = File.OpenWrite(path))
						{
							blob.DownloadTo(fileStream);
						}
						Console.WriteLine($"Downloaded {tup.artifactName}. Time cost={stopwatch.ElapsedMilliseconds}");
						return true;
					}
					catch (Exception value)
					{
						Console.WriteLine($"Downloading {tup.artifactName} failed.{Environment.NewLine}{value}");
						return false;
					}
				});
				tasks.Add(task);
			}
			Task[] tasks2 = tasks.ToArray();
			Task.WaitAll(tasks2, TimeSpan.FromMinutes(60.0));
			if (tasks.All((Task<bool> t) => t.IsCompletedSuccessfully && t.Result))
			{
				Console.WriteLine("Downloading tasks finished.");
				return true;
			}
			Console.WriteLine("Downloading tasks failed.");
			return false;
		}

		public (string version, string name) GetLatestArtifactName(string repoName, string versionFile, string pattern)
		{
			try
			{
				var blob = _containerClient.GetBlobClient(_options.RootDirectory + "/" + repoName + "/" + versionFile + ".txt");
				var properties = blob.GetProperties();
				var tags = properties.Value.Metadata;
				string version = tags["latestversion"];
				return (version, string.Format(pattern, version));
			}
			catch
			{
				Console.WriteLine("Get the latest artifact of repository {0} failed.", repoName);
				throw;
			}
		}

		public List<(string repoName, string version, string artifactName)> GetLatestVersion(string versionDetectFilePath)
		{
			List<(string, string, string)> latestVersions = new List<(string, string, string)>();
			Console.WriteLine("Detecting the latest artifacts...");
			List<(string, string, string)> repos = ReadRepos(versionDetectFilePath);
			foreach (var repo in repos)
			{
				(string, string) artifact = GetLatestArtifactName(repo.Item1, repo.Item2, repo.Item3);
				latestVersions.Add((repo.Item1, artifact.Item1, artifact.Item2));
			}
			return latestVersions;
		}

		private List<(string repoName, string versionFile, string pattern)> ReadRepos(string versionDetectFilePath)
		{
			return (from line in File.ReadLines(versionDetectFilePath, Encoding.UTF8)
					where !string.IsNullOrEmpty(line) && !line.Trim().StartsWith("//") && !line.Trim().StartsWith("#")
					select line).Select(delegate (string repoNameWithArtifact)
					{
						List<string> list = (from s in repoNameWithArtifact.Split(new char[2] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries)
											 select s.Trim()).ToList();
						if (list.Count < 3)
						{
							throw new Exception("Some columns is missing in the line '" + repoNameWithArtifact + "'.");
						}
						return (list[0], list[1], list[2]);
					}).ToList();
		}

		
	}
}