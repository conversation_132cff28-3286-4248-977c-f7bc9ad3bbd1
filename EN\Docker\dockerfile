FROM gcescr.azurecr.io/gces-amd64-8.0

ENV DEBIAN_FRONTEND=noninteractive

ENV DB_PROVIDER=Postgres
ENV DB_HOST=localhost
ENV DB_PORT=5432
ENV DB_USER=postgres
ENV DB_PASSWORD=postgres
ENV SINGLE_DATABASE_MODE=false
ENV DB_NAME_WYN=wyn
ENV DB_NAME_SERVERDATA=wynserverdata
ENV DB_NAME_DATACACHE=wyndatacache
ENV DB_NAME_IDENTITYSERVICE=wynis

ENV IMPORT_SAMPLES=true
ENV REQUIRE_HTTPS=false
ENV SINGLE_PROCESS_MODE=false
ENV ANALYSIS_DATABASE=MonetDB
ENV DEFAULT_LANGUAGE=English

RUN mkdir -p /var/monetdb5/dbfarm && \
    mkdir -p /usr/local/share/ca-certificates/wyn && \
    mkdir -p /usr/share/fonts/liberation-fonts-ttf-2.00.1

ADD --chmod=777 ./wyn ./wyn
COPY ./startup.sh /
COPY ./liberation-fonts-ttf-2.00.1 /usr/share/fonts/liberation-fonts-ttf-2.00.1
COPY ./jre /wyn/jre

EXPOSE 51980 5432 443

CMD ["/bin/bash", "/startup.sh"]