﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace gces_language_package_bundler
{
	internal class DisplayNameValidator
	{
		internal static Dictionary<string, Dictionary<string, List<ResourceFile>>> GetInvalidDisplayItems(Dictionary<string, List<ResourceFile>> resourceNameDict)
		{
			var invalidDisplayItems = new Dictionary<string, Dictionary<string, List<ResourceFile>>>();
			foreach (var resourceKeyVal in resourceNameDict)
			{
				var invalidItemDict = new Dictionary<string, List<ResourceFile>>();
				foreach (var resourceFile in resourceKeyVal.Value)
				{
					var validationRes = ValidateDisplayName(resourceFile.DisplayName);
					if (string.IsNullOrEmpty(validationRes)) continue;
					if (invalidItemDict.TryGetValue(validationRes, out var items) && items != null)
					{
						items.Add(resourceFile);
					}
					else
					{
						invalidItemDict.TryAdd(validationRes, new List<ResourceFile>() { resourceFile });
					}
				}

				if (invalidItemDict.Count > 0)
				{
					invalidDisplayItems.TryAdd(resourceKeyVal.Key, invalidItemDict);
				}

				var displayNameDuplicateItems = resourceKeyVal.Value.GroupBy(s => s.DisplayName).Where(g => g.Count() > 1).ToList();

				foreach (var duplicateItem in displayNameDuplicateItems)
				{
					var duplicatedDisplayName = duplicateItem.Select(d => d.DisplayName).Distinct();
					if (duplicatedDisplayName.Count() > 1)
					{
						if (invalidDisplayItems.TryGetValue(resourceKeyVal.Key, out var errorDict) && errorDict != null)
						{
							errorDict.TryAdd("Duplicated", duplicateItem.ToList());
						}
						else
						{
							invalidDisplayItems.TryAdd(resourceKeyVal.Key, new Dictionary<string, List<ResourceFile>>() { { "Duplicated", duplicateItem.ToList() } });
						}
					}
				}
			}

			return invalidDisplayItems;
		}

		private static string ValidateDisplayName(string displayName)
		{
			if (string.IsNullOrEmpty(displayName))
			{
				return "Empty";
			}

			if (displayName.Length > 31)
			{
				return "OverLength";
			}

			if (displayName.StartsWith("'") || displayName.EndsWith("'"))
			{
				return "Start|End with '";
			}

			if (string.Equals(displayName, "History", StringComparison.InvariantCultureIgnoreCase))
			{
				return "Reserved";
			}

			char[] invalidChars = new char[] { ':', '\\', '/', '?', '*', '[', ']' };
			if (invalidChars.Any(displayName.Contains))
			{
				return "Invalid Characters";
			}

			return string.Empty;
		}
	}
}
