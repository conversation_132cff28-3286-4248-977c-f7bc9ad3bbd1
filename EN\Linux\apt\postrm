#!/bin/bash

echo "post-removal checking ..."

if [ "$1" == "abort-upgrade" ]; then
  exit 1
fi

if [ "$1" == "purge" -o "$1" == "remove" ]; then
  # remove wyn.service
  if [ -L /etc/systemd/system/multi-user.target.wants/wyn.service ]; then
    sudo rm /etc/systemd/system/multi-user.target.wants/wyn.service
  fi
  if [ -f /lib/systemd/system/wyn.service ]; then
    sudo rm /lib/systemd/system/wyn.service
    sudo systemctl daemon-reload
  fi
  if [ -f /usr/lib/systemd/system/wyn.service ]; then
    sudo rm /usr/lib/systemd/system/wyn.service
    sudo systemctl daemon-reload
  fi
  if [ -f /etc/init.d/wyn ]; then
    sudo rm /etc/init.d/wyn
  fi
  sudo rm /opt/Wyn/.modules > /dev/null 2>&1
  sudo rm -rf /opt/Wyn/Monitor/conf > /dev/null 2>&1
  sudo rm -rf /opt/Wyn/Tools/RegisterTrialInfo > /dev/null 2>&1
  # remove wyn-database.service
  sudo systemctl stop wyn-database > /dev/null 2>&1
  if [ -f "/lib/systemd/system/wyn-database.service" ]; then
    sudo rm "/lib/systemd/system/wyn-database.service"
  fi
  if [ -f "/usr/lib/systemd/system/wyn-database.service" ]; then
    sudo rm "/usr/lib/systemd/system/wyn-database.service"
  fi
  if [ -L "/etc/systemd/system/multi-user.target.wants/wyn-database.service" ]; then
    sudo rm "/etc/systemd/system/multi-user.target.wants/wyn-database.service"
  fi
  # remove postgresql
  sudo rm -rf /opt/postgresql/pgsql > /dev/null 2>&1
  # remove jre
  sudo rm -rf /opt/Wyn/jre > /dev/null 2>&1
  # stop monetdbd
  sudo systemctl stop monetdbd > /dev/null 2>&1
  # remove monetdb
  echo "removing MonetDB..."
  sudo systemctl stop monetdbd > /dev/null 2>&1
  if [ -f "/lib/systemd/system/monetdbd.service" ]; then
    sudo rm "/lib/systemd/system/monetdbd.service"
  fi
  if [ -f "/usr/lib/systemd/system/monetdbd.service" ]; then
    sudo rm "/usr/lib/systemd/system/monetdbd.service"
  fi
  if [ -L "/etc/systemd/system/multi-user.target.wants/monetdbd.service" ]; then
    sudo rm "/etc/systemd/system/multi-user.target.wants/monetdbd.service"
  fi
  sudo systemctl daemon-reload > /dev/null 2>&1
  sudo rm -rf /etc/default/monetdb5-sql > /dev/null 2>&1
  sudo rm -rf /etc/init.d/monetdb5-sql > /dev/null 2>&1
  sudo rm -rf /etc/logrotate.d/monetdb > /dev/null 2>&1
  sudo rm -rf /run/monetdb > /dev/null 2>&1
  sudo rm -rf /usr/bin/mclient > /dev/null 2>&1
  sudo rm -rf /usr/bin/monetdb > /dev/null 2>&1
  sudo rm -rf /usr/bin/monetdbd > /dev/null 2>&1
  sudo rm -rf /usr/bin/mserver5 > /dev/null 2>&1
  sudo rm -rf /usr/bin/msqldump > /dev/null 2>&1
  sudo rm -rf /usr/lib/x86_64-linux-gnu/monetdb5 > /dev/null 2>&1
  sudo rm -rf /usr/lib/x86_64-linux-gnu/libmapi.* > /dev/null 2>&1
  sudo rm -rf /usr/lib/x86_64-linux-gnu/libmonetdb.* > /dev/null 2>&1
  sudo rm -rf /usr/share/doc/libmonetdb* > /dev/null 2>&1
  sudo rm -rf /usr/share/doc/monetdb* > /dev/null 2>&1
  sudo rm -rf /usr/share/man/man1/mclient.1.gz > /dev/null 2>&1
  sudo rm -rf /usr/share/man/man1/monetdb.1.gz > /dev/null 2>&1
  sudo rm -rf /usr/share/man/man1/monetdbd.1.gz > /dev/null 2>&1
  sudo rm -rf /usr/share/man/man1/mserver5.1.gz > /dev/null 2>&1
  sudo rm -rf /usr/share/man/man1/msqldump.1.gz > /dev/null 2>&1
  sudo rm -rf /var/lib/monetdb > /dev/null 2>&1
  sudo rm -rf /var/log/monetdb > /dev/null 2>&1
fi

exit 0
