FROM mcr.microsoft.com/dotnet/sdk:6.0

RUN apt-get update && apt-get install -y apt-transport-https dirmngr \
    && apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys 3FA7E0328081BFF6A14DA29AA6A19B38D3D831EF \
    && echo "deb https://download.mono-project.com/repo/debian stable-stretch main" | tee /etc/apt/sources.list.d/mono-official-stable.list \
    && apt-get update && apt-get install -y mono-devel \
    && curl -sL https://deb.nodesource.com/setup_16.x | bash \
    && apt-get install -y zip \
    && apt-get install -y nodejs \
    && npm i yarn -g 

COPY ossutil64 /opt/tools/ossutil64

RUN ln /opt/tools/ossutil64 /usr/local/bin/ossutil