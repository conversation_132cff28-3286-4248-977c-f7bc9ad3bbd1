#!/bin/bash

WynFiles=$1
LinuxInstallerPath=$2
wynbuildversion=$3

LoadBinariesPackages() {
    local __resultvar=$1
    local filePattern=$2
    local displayName=$3
    local sourcePath=$4

    files=($(find "$sourcePath" -name "$filePattern"))

    if [ ${#files[@]} -gt 0 ]; then
        eval $__resultvar="'${files[0]}'"
        echo "$displayName binaries package=${files[0]}"
    else
        eval $__resultvar="''"
        echo "$displayName binaries package not found."
    fi
}

echo "unzip artifacts to the content folder..."
rm -fr  $LinuxInstallerPath
mkdir -p $LinuxInstallerPath
unzip -q $WynFiles/server-en.*.zip -d $LinuxInstallerPath/Server
unzip -q $WynFiles/monitoring-netcore.*-en.zip -d $LinuxInstallerPath/Monitor
unzip -q $WynFiles/monitor-updater.*.zip -d $LinuxInstallerPath/MonitorUpdater
unzip -q $WynFiles/service-runner-en.*.zip -d $LinuxInstallerPath/ServiceRunner
unzip -q $WynFiles/dataset-and-account.*.zip -d $LinuxInstallerPath/Plugins
unzip -q $WynFiles/dashboard.*.zip -d $LinuxInstallerPath/Plugins
unzip -q $WynFiles/reporting.*.zip -d $LinuxInstallerPath/Plugins
unzip -q $WynFiles/tools.*.zip -d $LinuxInstallerPath/Tools
unzip -q $WynFiles/gces-monitoring-tools.*.zip -d $LinuxInstallerPath/
unzip -q $WynFiles/gcef-datasource-service.*.zip -d $LinuxInstallerPath/DataSourceService
unzip -q $WynFiles/gces-memorydb-service.*.zip -d $LinuxInstallerPath/MemoryDBService
unzip -q $WynFiles/analysis-model.*.zip -d $LinuxInstallerPath/
unzip -q $WynFiles/scheduler.service.*.zip -d $LinuxInstallerPath/SchedulerService
unzip -q $WynFiles/data-monitoring.*.zip -d $LinuxInstallerPath/
if [ ! -d "$WynFiles/dependencies/builtin_data" ]; then
    echo "extracting dependencies..."
    unzip -q $WynFiles/dependencies/Dependencies_EN_Linux_*.zip -d $WynFiles/dependencies/builtin_data
fi

echo "move files to the correct folder..."
mv $LinuxInstallerPath/Plugins/plugin  $LinuxInstallerPath/Plugins/Reporting
mv $LinuxInstallerPath/Plugins/cot-worker  $LinuxInstallerPath/CotWorker
mv $LinuxInstallerPath/Plugins/dashboard  $LinuxInstallerPath/Plugins/Dashboard
mv $LinuxInstallerPath/Plugins/dashboard-worker  $LinuxInstallerPath/DashboardWorker
mv $LinuxInstallerPath/Plugins/dataset  $LinuxInstallerPath/Plugins/Dataset
mv $LinuxInstallerPath/Plugins/account  $LinuxInstallerPath/Plugins/Account
mv $LinuxInstallerPath/Plugins/worker  $LinuxInstallerPath/ReportingWorker
mv $LinuxInstallerPath/EncryptOrDecryptString  $LinuxInstallerPath/Tools/EncryptOrDecryptString
mv $LinuxInstallerPath/analysis  $LinuxInstallerPath/Plugins/AnalysisModel
mv $LinuxInstallerPath/dataMonitoring  $LinuxInstallerPath/Plugins/DataMonitoring
cp -f ./Linux/install_wyn_datasource_service.sh $LinuxInstallerPath/DataSourceService/install.sh
cp -f ./Linux/uninstall_wyn_datasource_service.sh $LinuxInstallerPath/DataSourceService/uninstall.sh

cp -r $LinuxInstallerPath/Tools/SecurityProviders $LinuxInstallerPath/Server/SecurityProviders
rm -fr $LinuxInstallerPath/Tools/ExternalLoginProviders
mkdir -p $LinuxInstallerPath/Server/official_language_packages
cp -r $WynFiles/language-package-*-EN.zip $LinuxInstallerPath/Server/official_language_packages/

echo "extracting style files..."
themePackages=("analysis-model-styles.*.zip" "dashboard-styles.*.zip" "dataset-and-account-styles.*.zip" "reporting-styles.*.zip" "server-styles.*.zip")
styleFilePath="StylesCache/temp"
styleFilePath2="StylesCache/$wynbuildversion"

mkdir -p "$styleFilePath"
mkdir -p "$styleFilePath2"

for themePackage in "${themePackages[@]}"; do
    themeFilePath=""
    LoadBinariesPackages themeFilePath "$themePackage" "$(echo "$themePackage" | cut -d '.' -f 1)" $WynFiles

    directoryName=$(basename "$themeFilePath")
    directoryName="${directoryName%.*}"

    if [[ $directoryName == *dev ]]; then
        directoryName="${directoryName%-*}"
    fi

    unzip -q "$themeFilePath" -d "$styleFilePath/$directoryName"
    cp -r "$styleFilePath/$directoryName/src/." "$styleFilePath/$directoryName"
    rm -rf "$styleFilePath/$directoryName/src"
done

cd $styleFilePath
zip -q -r "../$wynbuildversion/styleFiles.zip" ./*
cd ../../

mkdir -p $LinuxInstallerPath/Server/StylesCache
cp -r $styleFilePath2 $LinuxInstallerPath/Server/StylesCache/

rm -fr $styleFilePath
rm -fr $styleFilePath2

rm -fr "$LinuxInstallerPath/Plugins/Web Contents"

shopt -s globstar
rm -fr $LinuxInstallerPath/**/*.pdb
rm -fr $LinuxInstallerPath/**/appsettings.*.json

rm -fr $LinuxInstallerPath/CotWorker/Firebird3Embedded
rm -fr $LinuxInstallerPath/Plugins/Dataset/Firebird3Embedded
rm -fr $LinuxInstallerPath/Server/Firebird3Embedded

cp -r $WynFiles/dependencies/builtin_data/GeoJsonData $LinuxInstallerPath/Server
cp -r $WynFiles/dependencies/builtin_data/sample_files $LinuxInstallerPath/Server
cp -r $WynFiles/dependencies/builtin_data/font_files $LinuxInstallerPath/Server
cp -r $WynFiles/dependencies/builtin_data/map $LinuxInstallerPath/Server/wwwroot/
cp -r $WynFiles/dependencies/builtin_data/3DScene $LinuxInstallerPath/Server/wwwroot/
cp -r $WynFiles/dependencies/builtin_data/sampledata $LinuxInstallerPath/
cp -r $WynFiles/dependencies/builtin_data/Tools/ValidateDatabaseConnect $LinuxInstallerPath/Tools/
cp -r $WynFiles/dependencies/builtin_data/Tools/ARS_Migration $LinuxInstallerPath/Tools/
cp -f $LinuxInstallerPath/Plugins/Dataset/OdbcLib/linux/System.Data.Odbc.dll $LinuxInstallerPath/Plugins/Dataset/

mkdir -p $LinuxInstallerPath/Server/builtin_language_packages
cp -f $WynFiles/ko-KR.v*.xlsx $LinuxInstallerPath/Server/builtin_language_packages/

echo  $wynbuildversion > $LinuxInstallerPath/Server/version.txt