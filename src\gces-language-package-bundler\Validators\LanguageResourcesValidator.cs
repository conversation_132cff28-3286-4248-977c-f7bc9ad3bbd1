using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;
using System.Threading.Tasks;

namespace gces_language_package_bundler
{
	public class LanguageResourcesValidationResult
	{
		public bool IsValid { get; set; } = true;
		public List<string> LanguageArtifacts { get; set; } = new List<string>();
		public LngResourceConfigValidateRes ValidationDetails { get; set; }
		public string ErrorMessage { get; set; }
	}

	internal class LanguageResourcesValidator
	{
		public static async Task<LanguageResourcesValidationResult> ValidateLanguageResources(
			string artifactsDir
		)
		{
			var result = new LanguageResourcesValidationResult();

			var lngArtifacts = Directory.GetFiles(artifactsDir, "*-languages*").ToList();
			if (lngArtifacts.Count == 0)
			{
				result.IsValid = false;
				result.ErrorMessage = "bundle language package error. There is no any language artifact ...";
				Console.WriteLine(result.ErrorMessage);
				return result;
			}

			result.LanguageArtifacts = lngArtifacts;
			result.ValidationDetails = await Utils.ValidateLanguageResources(lngArtifacts);
			result.IsValid = result.ValidationDetails.Valid;

			return result;
		}

		public static void PrintValidationErrors(LngResourceConfigValidateRes validateRes)
		{
			if (validateRes.Valid)
				return;

			var jsonSerializerOptions = new JsonSerializerOptions()
			{
				WriteIndented = true,
				Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
			};
			Console.OutputEncoding = Encoding.UTF8;
			Console.WriteLine(
				"------------------language resource config error---------------------------"
			);

			if (validateRes.ModuleAndNameDuplicatedItems.Count > 0)
			{
				PrintModuleAndNameDuplicatedErrors(
					validateRes.ModuleAndNameDuplicatedItems,
					jsonSerializerOptions
				);
			}

			if (validateRes.InvalidOrderValueItems.Values.Any(v => v.Count > 0))
			{
				PrintInvalidOrderValueErrors(
					validateRes.InvalidOrderValueItems,
					jsonSerializerOptions
				);
			}

			if (validateRes.MultiOrdersItems.Values.Any(v => v.Count > 0))
			{
				PrintMultiOrdersErrors(validateRes.MultiOrdersItems, jsonSerializerOptions);
			}

			if (validateRes.OrderDuplicatedItems.Values.Any(v => v.Count > 0))
			{
				PrintOrderDuplicatedErrors(validateRes.OrderDuplicatedItems, jsonSerializerOptions);
			}

			if (validateRes.MultiObsoleteItems.Values.Any(v => v.Count > 0))
			{
				PrintMultiObsoleteErrors(validateRes.MultiObsoleteItems, jsonSerializerOptions);
			}

			if (validateRes.InvalidDisplayNameItems.Values.Any(v => v.Count > 0))
			{
				PrintInvalidDisplayNameErrors(
					validateRes.InvalidDisplayNameItems,
					jsonSerializerOptions
				);
			}

			Console.WriteLine(
				"------------------language resource config error---------------------------"
			);
		}

		private static void PrintModuleAndNameDuplicatedErrors(
			Dictionary<string, IGrouping<string, ResourceFile>> duplicatedItems,
			JsonSerializerOptions options
		)
		{
			Console.WriteLine();
			Console.WriteLine(
				"------------------Module and display name must be unique in the whole package---------------------------"
			);
			foreach (var errorItem in duplicatedItems)
			{
				var dict = new Dictionary<string, List<ResourceFile>>()
				{
					{ errorItem.Value.Key, errorItem.Value.ToList() }
				};
				var duplicatedItemsJson = JsonSerializer.Serialize(dict, options);
				Console.WriteLine(duplicatedItemsJson);
			}
			Console.WriteLine(
				"------------------Module and display name must be unique in the whole package---------------------------"
			);
			Console.WriteLine();
		}

		private static void PrintInvalidOrderValueErrors(
			Dictionary<string, List<ResourceFile>> invalidOrderItems,
			JsonSerializerOptions options
		)
		{
			Console.WriteLine();
			Console.WriteLine(
				"------------------The order value should be assigned, less than 10000 and greater than 0---------------------------"
			);
			foreach (var errorItem in invalidOrderItems)
			{
				var dict = new Dictionary<string, List<ResourceFile>>()
				{
					{ errorItem.Key, errorItem.Value }
				};
				var orderNullItems = JsonSerializer.Serialize(dict, options);
				Console.WriteLine(orderNullItems);
			}
			Console.WriteLine(
				"------------------The order value should be assigned, less than 10000 and greater than 0---------------------------"
			);
			Console.WriteLine();
		}

		private static void PrintMultiOrdersErrors(
			Dictionary<string, List<ResourceFile>> multiOrdersItems,
			JsonSerializerOptions options
		)
		{
			Console.WriteLine(
				"---------------------The same file can have only one order value------------------------"
			);
			foreach (var errorItem in multiOrdersItems)
			{
				var dict = new Dictionary<string, List<ResourceFile>>()
				{
					{ errorItem.Key, errorItem.Value }
				};
				var multiOrdersItemsJson = JsonSerializer.Serialize(dict, options);
				Console.WriteLine(multiOrdersItemsJson);
			}
			Console.WriteLine(
				"---------------------The same file can have only one order value------------------------"
			);
		}

		private static void PrintOrderDuplicatedErrors(
			Dictionary<string, List<IGrouping<int?, ResourceFile>>> orderDuplicatedItems,
			JsonSerializerOptions options
		)
		{
			Console.WriteLine();
			Console.WriteLine(
				"---------------------Different documents must have different orders------------------------"
			);
			foreach (var errorItem in orderDuplicatedItems)
			{
				var dict = new Dictionary<string, List<List<ResourceFile>>>()
				{
					{ errorItem.Key, errorItem.Value.Select(group => group.ToList()).ToList() }
				};
				var orderDuplicatedItemsJson = JsonSerializer.Serialize(dict, options);
				Console.WriteLine(orderDuplicatedItemsJson);
			}
			Console.WriteLine(
				"---------------------Different documents must have different orders------------------------"
			);
			Console.WriteLine();
		}

		private static void PrintMultiObsoleteErrors(
			Dictionary<string, List<ResourceFile>> multiObsoleteItems,
			JsonSerializerOptions options
		)
		{
			Console.WriteLine(
				"---------------------The same file can have only one obsolete value------------------------"
			);
			foreach (var errorItem in multiObsoleteItems)
			{
				var dict = new Dictionary<string, List<ResourceFile>>()
				{
					{ errorItem.Key, errorItem.Value }
				};
				var multiObsoleteItemsJson = JsonSerializer.Serialize(dict, options);
				Console.WriteLine(multiObsoleteItemsJson);
			}
			Console.WriteLine(
				"---------------------The same file can have only one obsolete value------------------------"
			);
		}

		private static void PrintInvalidDisplayNameErrors(
			Dictionary<string, Dictionary<string, List<ResourceFile>>> invalidDisplayNameItems,
			JsonSerializerOptions options
		)
		{
			Console.WriteLine();
			Console.WriteLine(
				"---------------------The display name cannot be empty, cannot be 'History', connot start/end with ', cannot be longer than 31, cannot be duplicated, cannot contain ':', '\\', '/', '? ', '*', '[', ']'------------------------"
			);
			foreach (var errorItem in invalidDisplayNameItems)
			{
				var dict = new Dictionary<string, Dictionary<string, List<ResourceFile>>>()
				{
					{ errorItem.Key, errorItem.Value }
				};
				var invalidDisplayNameItemsJson = JsonSerializer.Serialize(dict, options);
				Console.WriteLine(invalidDisplayNameItemsJson);
			}
			Console.WriteLine(
				"The display name cannot be empty, cannot be 'History', connot start/end with ', cannot be longer than 31, cannot be duplicated, cannot contain ':', '\\', '/', '? ', '*', '[', ']'"
			);
			Console.WriteLine();
		}
	}
}
