[{"Module": "Scheduler", "DisplayName": "Schedule Task", "Order": 1, "Obsolete": false, "Path": "gces-server/src/Gces.Scheduler.Plugin/clientApp/src/localization/pl.ts"}, {"Module": "Scheduler", "DisplayName": "Dashboard Task", "Order": 2, "Obsolete": false, "Path": "gces-server/src/Gces.Scheduler.Plugin/clientApp/src/localization/dashboard/scheduling.pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Floor Plan 1", "Order": 3, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/floorPlanDesigner/localization/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Floor Plan 2", "Order": 4, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/floorPlanDesigner/localization/portalLocaleData/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Hierarchical Map 1", "Order": 5, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/geoJson/localization/pl.json"}, {"Module": "Shared Resources", "DisplayName": "Hierarchical Map 2", "Order": 6, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/geoJson/localization/portalLocaleData/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Document Theme 1", "Order": 7, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/themeDesigner/localization/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Document Theme 2", "Order": 8, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/themeDesigner/localization/portalLocaleData/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Document Theme Viewer", "Order": 9, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/themePreview/localization/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Visualization 1", "Order": 10, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/visual/localization/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Visualization 2", "Order": 11, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/visual/localization/portalLocaleData/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Web Page 1", "Order": 12, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/webPages/localization/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Web Page 2", "Order": 13, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/webPages/localization/portalLocaleData/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Font 1", "Order": 14, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/font/localization/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Font 2", "Order": 15, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/font/localization/portalLocaleData/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Language Resource 1", "Order": 16, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResource/localization/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Language Resource 2", "Order": 17, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResource/localization/portalLocaleData/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Language Resource Viewer 1", "Order": 18, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResourcePreview/localization/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Language Resource Viewer 2", "Order": 19, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/languageResourcePreview/localization/portalLocaleData/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Document Binders Viewer 1", "Order": 20, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBindersPreview/localization/en.ts"}, {"Module": "Shared Resources", "DisplayName": "Document Binders Viewer 2", "Order": 21, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBindersPreview/localization/portalLocaleData/en.ts"}, {"Module": "Portal", "DisplayName": "Error <PERSON>", "Order": 22, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Error/localization/pl.ts"}, {"Module": "Admin Portal", "DisplayName": "Design Shell Component", "Order": 23, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/AdminPortal/localization/designer-shell/pl.json"}, {"Module": "Portal", "DisplayName": "Admin Portal", "Order": 24, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/AdminPortal/localization/pl.json"}, {"Module": "Portal", "DisplayName": "Portal Common Info", "Order": 25, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/pl.json"}, {"Module": "Portal", "DisplayName": "Category", "Order": 26, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/TagsPanel/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Document Types", "Order": 27, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/ResourcePanel/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Category Editor", "Order": 28, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/TagsEditor/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Work Space", "Order": 29, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/WorkspaceBase/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Upload", "Order": 30, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/Upload/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Search(mobile portal)", "Order": 31, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/MobileSearch/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Resource Portal", "Order": 32, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/EN/ResourcePortal/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Document Portal", "Order": 33, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/EN/DocumentPortal/localization/pl.ts"}, {"Module": "Admin Portal", "DisplayName": "System Diagnostic", "Order": 34, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/SystemDiagnostic/localization/pl.ts"}, {"Module": "Admin Portal", "DisplayName": "Node Management", "Order": 35, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/NodeManagement/localization/pl.json"}, {"Module": "Admin Portal", "DisplayName": "<PERSON>t Log", "Order": 36, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/AuditLogConfiguration/localization/pl.json"}, {"Module": "Portal", "DisplayName": "Copy Url Dialog", "Order": 37, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/CopyUrlDialog/localization/pl.ts"}, {"Module": "Admin Portal", "DisplayName": "Document Permission Editor", "Order": 38, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/DocsPermissionEditor/localization/pl.json"}, {"Module": "Admin Portal", "DisplayName": "Document Category Editor", "Order": 39, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/DocumentsTagsEditor/localization/pl.ts"}, {"Module": "Admin Portal", "DisplayName": "External Storage", "Order": 40, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/ExternalStorage/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Personal Category", "Order": 41, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/PersonalTagManagement/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Profile Setting", "Order": 42, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/ProfileEditor/localization/pl.ts"}, {"Module": "Admin Portal", "DisplayName": "Schedule Templates", "Order": 43, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/Schedule/localization/pl.ts"}, {"Module": "Admin Portal", "DisplayName": "System Management", "Order": 44, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/SystemManagement/localization/pl.json"}, {"Module": "Admin Portal", "DisplayName": "Task Monitor", "Order": 45, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/WorkerTiles/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Recycle Bin", "Order": 46, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/SoftDeletedDocument/localization/pl.ts"}, {"Module": "Data Monitoring", "DisplayName": "Data Monitoring 1", "Order": 47, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DataMonitoring/localization/pl.ts"}, {"Module": "Data Monitoring", "DisplayName": "Data Monitoring 2", "Order": 48, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DataMonitoring/localization/editor/pl.json"}, {"Module": "Portal", "DisplayName": "Document Draft 1", "Order": 49, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DocumentDraftList/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Document Draft 2", "Order": 50, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/DocumentDraftPanel/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Document Draft 3", "Order": 51, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/PublishRequestList/localization/pl.ts"}, {"Module": "Admin Portal", "DisplayName": "File Extraction Setting", "Order": 52, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/FileDataSourceConfiguration/localization/pl.ts"}, {"Module": "Document Order", "DisplayName": "Document Type", "Order": 53, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/DocumentTypesLocaleData/pl.json"}, {"Module": "Document Order", "DisplayName": "Document Extension", "Order": 54, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/DocumentExtLocaleData/pl.json"}, {"Module": "API", "DisplayName": "Common Errors(Server Plugin)", "Order": 55, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/localization/ApiErrorsLocaleData/v2/pl.json"}, {"Module": "Portal", "DisplayName": "Designer Work Sheets", "Order": 56, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/Designer/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Preview Tabs", "Order": 57, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Modules/PreviewTabs/localization/pl.ts"}, {"Module": "Portal", "DisplayName": "Document Binders", "Order": 58, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBinder/localization/portalLocalData/pl.ts"}, {"Module": "Shared Resources", "DisplayName": "Document Binders designer", "Order": 59, "Obsolete": false, "Path": "gces-server/src/Gces.SharedResources.Plugin/clientApp/src/documentBinder/localization/pl.ts"}, {"Module": "Identity", "DisplayName": "License", "Order": 60, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/license/localization/pl.ts"}, {"Module": "Identity", "DisplayName": "<PERSON><PERSON>", "Order": 61, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/login/localization/pl.ts"}, {"Module": "Identity", "DisplayName": "Login <PERSON>", "Order": 62, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/login-to-tenant/localization/pl.ts"}, {"Module": "Identity", "DisplayName": "Trial Info Register", "Order": 63, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/trial-info-register/localization/pl.ts"}, {"Module": "Identity", "DisplayName": "Verification Code Page", "Order": 64, "Obsolete": false, "Path": "gcef-identity-service/Grapecity.Enterprise.Identity/ClientApp/i18ns/verification/localization/pl.ts"}, {"Module": "API", "DisplayName": "Common Errors(Dataset Plugin)", "Order": 65, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Dataset Type", "Order": 66, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/documentSectionRegisterLocaleData/pl.ts"}, {"Module": "Dataset", "DisplayName": "Parameter Panel", "Order": 67, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/previewParameterRegisterLocaleData/pl.ts"}, {"Module": "Dataset", "DisplayName": "Message Box", "Order": 68, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/MessageBox/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Notification", "Order": 69, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/notifications/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Push and Streaming Dataset", "Order": 70, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/singleTableDataset/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Dataset Cache", "Order": 71, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/CotManagement/localization/pl.ts"}, {"Module": "Data Source", "DisplayName": "Data Providers", "Order": 72, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DataProviders/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Cache Dataset", "Order": 73, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasetDesigner/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Streaming Dataset", "Order": 74, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/StreamingDatasetDesigner/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Push Dataset", "Order": 75, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/PushDatasetDesigner/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Native Query Dataset 1", "Order": 76, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/NativeQueryDatasetDesigner/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Native Query Dataset 2", "Order": 77, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/NativeQueryDataset/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Dataset Sidebar", "Order": 78, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasetSidebarTab/localization/pl.ts"}, {"Module": "Data Source", "DisplayName": "Data Source Designer", "Order": 79, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasourceDesigner/localization/pl.json"}, {"Module": "Data Source", "DisplayName": "Data Source Sidebar", "Order": 80, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/DatasourceSidebarTab/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Dataset Viewer", "Order": 81, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/PreviewDataset/localization/pl.ts"}, {"Module": "Data Source", "DisplayName": "Data Source Viewer", "Order": 82, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/PreviewDatasource/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Dataset Basic Info", "Order": 83, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/localization/portal.pl.json"}, {"Module": "Dataset", "DisplayName": "Prepare Data 1", "Order": 84, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/creations/PrepareData/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Prepare Data 2", "Order": 85, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/creations/PrepareData/localization/portalRegisterLocaleData/pl.ts"}, {"Module": "Data Source", "DisplayName": "Append|Overwrite Data", "Order": 86, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/ExcelData/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Incremental Update", "Order": 87, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/IncrementalUpdateCOT/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Refresh COT", "Order": 88, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/RefreshCOT/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Streaming Dataset Endpoints", "Order": 89, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/StreamingDatasetInsertDataDialog/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Push Dataset Endpoints", "Order": 90, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/PushDataset/localization/pl.ts"}, {"Module": "Dataset", "DisplayName": "Dataset Export", "Order": 91, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Register/verbs/Export/localization/pl.ts"}, {"Module": "Data Model", "DisplayName": "Semantic Model 1", "Order": 92, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/SemanticDesigner/localization/pl.ts"}, {"Module": "Data Model", "DisplayName": "Semantic Model 2", "Order": 93, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/SemanticDesigner/localization/dataset.pl.ts"}, {"Module": "Data Model", "DisplayName": "Semantic Model 3", "Order": 94, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/SemanticDesigner/localization/portalData.pl.ts"}, {"Module": "Dataset", "DisplayName": "Function Description", "Order": 95, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/Resource/FnDescription-pl.json"}, {"Module": "Dataset", "DisplayName": "SQL Function Description", "Order": 96, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/Resource/SqlFunction-pl.json"}, {"Module": "Dataset", "DisplayName": "Data Plugin Tile", "Order": 97, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/TilesRegister/localization/pl.ts"}, {"Module": "Account", "DisplayName": "Account P<PERSON><PERSON>", "Order": 98, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/localization/portalLocaleData/pl.js"}, {"Module": "Account", "DisplayName": "Account Plugin Basic info 1", "Order": 99, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/localization/pl.js"}, {"Module": "Account", "DisplayName": "Account Plugin Basic info 2", "Order": 100, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/Common/localization/pl.json"}, {"Module": "Account", "DisplayName": "Role Management", "Order": 101, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/RoleManagement/localization/pl.json"}, {"Module": "Account", "DisplayName": "Tenant Management", "Order": 102, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/TenantManagement/localization/pl.ts"}, {"Module": "Account", "DisplayName": "User Management", "Order": 103, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/UserManagement/localization/pl.ts"}, {"Module": "IoT Designer", "DisplayName": "IoT Designer", "Order": 104, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/IoTDataDesigner/localization/pl.json"}, {"Module": "Account", "DisplayName": "Sync Settings", "Order": 105, "Obsolete": false, "Path": "gces-common/src/Gces.Account.Plugin/ClientApp/Register/SynchronizationSetting/localization/pl.json"}, {"Module": "Data Model", "DisplayName": "Data Model Designer <PERSON><PERSON><PERSON>", "Order": 106, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/localization/error.pl.json"}, {"Module": "Data Model", "DisplayName": "Data Model Designer", "Order": 107, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/localization/model.pl.json"}, {"Module": "Data Model", "DisplayName": "Data Model Designer2", "Order": 108, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/localization/wizard.pl.json"}, {"Module": "Data Model", "DisplayName": "Data Model Basic Info", "Order": 109, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/Register/localization/portal.pl.ts"}, {"Module": "Data Model", "DisplayName": "Data Model Message Box", "Order": 110, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/Common/MessageBox/localization/pl.ts"}, {"Module": "Data Model", "DisplayName": "Tasks", "Order": 111, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/TaskTab/localization/pl.ts"}, {"Module": "Data Model", "DisplayName": "Reload Data", "Order": 112, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/PortalReload/localization/pl.json"}, {"Module": "Data Model", "DisplayName": "Data Model Document List", "Order": 113, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/PortalReload/localization/portalLocaleData/pl.ts"}, {"Module": "Data Model", "DisplayName": "Data Model Cache Management", "Order": 114, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/DataModelCacheManagement/localization/pl.ts"}, {"Module": "Data Model", "DisplayName": "API Error", "Order": 115, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/Common/localization/apiError/pl.json"}, {"Module": "Dashboard", "DisplayName": "Dashboard Setting", "Order": 116, "Obsolete": false, "Path": "gces-dashboard/frontend/src/adminPortal/localization/pl.json"}, {"Module": "Dashboard", "DisplayName": "Portal 1", "Order": 117, "Obsolete": false, "Path": "gces-dashboard/frontend/src/portalRegister/localization/portal/pl.json"}, {"Module": "Dashboard", "DisplayName": "Portal 2", "Order": 118, "Obsolete": false, "Path": "gces-dashboard/frontend/src/portalRegister/localization/dashboard/pl.json"}, {"Module": "Dashboard", "DisplayName": "Portal 3", "Order": 119, "Obsolete": false, "Path": "gces-dashboard/frontend/src/portalRegister/localization/scene/pl.json"}, {"Module": "Dashboard", "DisplayName": "3D Scene", "Order": 120, "Obsolete": false, "Path": "gces-dashboard/frontend/packages/sceneCore/locales/scene/en.json"}, {"Module": "Dashboard", "DisplayName": "Dashboard", "Order": 121, "Obsolete": false, "Path": "gces-dashboard/frontend/packages/dashboardCore/locales/dashboard/pl.json"}, {"Module": "Dashboard", "DisplayName": "Dashboard Dependencies", "Order": 122, "Obsolete": false, "Path": "gces-dashboard/frontend/packages/dashboardCore/locales/third/pl.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Common", "Order": 123, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/localization/pl/common.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Notifications", "Order": 124, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/localization/pl/reporting.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Settings Labels", "Order": 125, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/localization/pl/portal.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Common - Portals", "Order": 126, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/common/localization/pl/portal.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Features - Portals", "Order": 127, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/common/localization/pl/reporting.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Export Templates", "Order": 128, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/ExportSettingTemplates/localization/pl.json"}, {"Module": "Reporting", "DisplayName": "Reporting - User Functions", "Order": 129, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/UserFunctions/localization/pl.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Settings", "Order": 130, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/adminPortal/ReportingSettings/localization/pl.json"}, {"Module": "Reporting", "DisplayName": "Reporting - Enums - Portals", "Order": 131, "Obsolete": false, "Path": "gces-reporting/gces-reporting/src/Plugin/client/common/localization/pl/enums.json"}, {"Module": "Report Designer", "DisplayName": "Designer - <PERSON>", "Order": 132, "Obsolete": false, "Path": "gces-reporting/gces-reporting-designer/src/i18n/wynReportDesigner/pl.json"}, {"Module": "Report Designer", "DisplayName": "Designer - Dataset Validation", "Order": 133, "Obsolete": false, "Path": "gces-reporting/gces-reporting-designer/src/i18n/wynReportDesignerDataSetSchemaErrors/pl.json"}, {"Module": "Report Designer", "DisplayName": "Core - Adorners", "Order": 134, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-adorners.json"}, {"Module": "Report Designer", "DisplayName": "Core - Captions", "Order": 135, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-captions.json"}, {"Module": "Report Designer", "DisplayName": "Core - Chart Wizard", "Order": 136, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-chartWizard.json"}, {"Module": "Report Designer", "DisplayName": "Core - Common", "Order": 137, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-common.json"}, {"Module": "Report Designer", "DisplayName": "Core - Components", "Order": 138, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-components.json"}, {"Module": "Report Designer", "DisplayName": "Core - Context Actions", "Order": 139, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-contextActions.json"}, {"Module": "Report Designer", "DisplayName": "Core - Defaults", "Order": 140, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-defaults.json"}, {"Module": "Report Designer", "DisplayName": "Core - Dialogs", "Order": 141, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-dialogs.json"}, {"Module": "Report Designer", "DisplayName": "Core - Documents API", "Order": 142, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-documentsAPI.json"}, {"Module": "Report Designer", "DisplayName": "Core - Enums", "Order": 143, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-enums.json"}, {"Module": "Report Designer", "DisplayName": "Core - Notifications 1", "Order": 144, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-error.json"}, {"Module": "Report Designer", "DisplayName": "Core - Expression Fields", "Order": 145, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-expressionFields.json"}, {"Module": "Report Designer", "DisplayName": "Core - Filters", "Order": 146, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-filters.json"}, {"Module": "Report Designer", "DisplayName": "Core - Group Editor", "Order": 147, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-groupEditor.json"}, {"Module": "Report Designer", "DisplayName": "Core - Labels", "Order": 148, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-labels.json"}, {"Module": "Report Designer", "DisplayName": "Core - <PERSON><PERSON>", "Order": 149, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-marginsSizes.json"}, {"Module": "Report Designer", "DisplayName": "Core - Name Templates", "Order": 150, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-nameTemplates.json"}, {"Module": "Report Designer", "DisplayName": "Core - Notifications 2", "Order": 151, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-notifications.json"}, {"Module": "Report Designer", "DisplayName": "Core - Page Sizes", "Order": 152, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-pageSizes.json"}, {"Module": "Report Designer", "DisplayName": "Core - Parameters View Editor", "Order": 153, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-parametersViewEditor.json"}, {"Module": "Report Designer", "DisplayName": "Core - Property Descriptors", "Order": 154, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-propertyDescriptors.json"}, {"Module": "Report Designer", "DisplayName": "Core - Property Editors", "Order": 155, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-propertyEditors.json"}, {"Module": "Report Designer", "DisplayName": "Core - Report Items", "Order": 156, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-reportItems.json"}, {"Module": "Report Designer", "DisplayName": "Core - Report Items Labels", "Order": 157, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-romLabels.json"}, {"Module": "Report Designer", "DisplayName": "Core - Tablix Wizard", "Order": 158, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-tablixWizard.json"}, {"Module": "Report Designer", "DisplayName": "Core - Validation", "Order": 159, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-validationErrors.json"}, {"Module": "Report Designer", "DisplayName": "Core - Notifications 3", "Order": 160, "Obsolete": false, "Path": "gces-reporting/ar-wd-core/lib/i18n/pl/pl-warning.json"}, {"Module": "Report Viewer", "DisplayName": "Viewer - Common", "Order": 161, "Obsolete": false, "Path": "gces-reporting/gces-reporting-viewer/src/i18n/pl/wynReportViewer.json"}, {"Module": "Report Viewer", "DisplayName": "Viewer - Core", "Order": 162, "Obsolete": false, "Path": "gces-reporting/gces-reporting-viewer/src/i18n/pl/viewer.json"}, {"Module": "Report Viewer", "DisplayName": "Viewer - Notifications", "Order": 163, "Obsolete": false, "Path": "gces-reporting/gces-reporting-viewer/src/i18n/pl/wynReportViewerErrors.json"}, {"Module": "Reporting Components", "DisplayName": "Parameter Panel - Common", "Order": 164, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/pl/params.json"}, {"Module": "Reporting Components", "DisplayName": "Parameter Panel - Validation", "Order": 165, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/pl/paramsValidation.json"}, {"Module": "Reporting Components", "DisplayName": "Parameter Panel - View", "Order": 166, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/pl/paramsView.json"}, {"Module": "Reporting Components", "DisplayName": "Parameters", "Order": 167, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Parameters/i18n/pl/wynReportParameters.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Enums", "Order": 168, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/enums.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Common", "Order": 169, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/common.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Delivery", "Order": 170, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/delivery.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - CSV", "Order": 171, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/export-csv.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Word", "Order": 172, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/export-docx.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Excel", "Order": 173, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/export-excel.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Excel Data", "Order": 174, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/export-excel-data.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - HTML", "Order": 175, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/export-html.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - Image", "Order": 176, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/export-image.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - JSON", "Order": 177, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/export-json.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - PDF", "Order": 178, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/export-pdf.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - TXT", "Order": 179, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/export-txt.json"}, {"Module": "Reporting Components", "DisplayName": "Export Settings - XML", "Order": 180, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/Exports/i18n/pl/export-xml.json"}, {"Module": "Reporting Utils", "DisplayName": "Http Client", "Order": 181, "Obsolete": false, "Path": "gces-reporting/gces-reporting-utils/src/httpClient/i18n/pl.json"}, {"Module": "Reporting Utils", "DisplayName": "Report Languages", "Order": 182, "Obsolete": false, "Path": "gces-reporting/gces-reporting-utils/src/reportLanguages/i18n/pl.json"}, {"Module": "Reporting Utils", "DisplayName": "Custom Parameters View", "Order": 183, "Obsolete": false, "Path": "gces-reporting/gces-reporting-utils/src/parametersView/i18n/pl.json"}, {"Module": "Component Library", "DisplayName": "Document Reference", "Order": 184, "Obsolete": false, "Path": "gces-js-common/packages/gces-references-component/src/localization/pl.ts"}, {"Module": "Component Library", "DisplayName": "Share", "Order": 185, "Obsolete": false, "Path": "gces-js-common/packages/wyn-components/src/localization/pl.ts"}, {"Module": "ETL", "DisplayName": "ETL Designer", "Order": 186, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Add Formulas", "Order": 187, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/AddFormula/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Combine Records", "Order": 188, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/AppendRecords/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Combine Fields", "Order": 189, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/CombineFields/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Filter Records", "Order": 190, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/FilterRecords/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Group By", "Order": 191, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/GroupBy/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Input Source", "Order": 192, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/InputSource/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Join <PERSON>", "Order": 193, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/JoinData/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Output Target", "Order": 194, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/OutputTarget/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Transpose Row to Col", "Order": 195, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/Pivot/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Re<PERSON>ve Duplicates", "Order": 196, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/RemoveDuplicates/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Select <PERSON>", "Order": 197, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/SelectFields/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Set Field Type", "Order": 198, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/SetFieldType/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Split Records", "Order": 199, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/SplitRecords/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Transpose Col to Row", "Order": 200, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/Unpivot/localization/pl.json"}, {"Module": "ETL", "DisplayName": "Command-Common Localization", "Order": 201, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/ETLDesigner/commands/CommonLocalization/pl.json"}, {"Module": "Dashboard", "DisplayName": "Chat Analysis", "Order": 202, "Obsolete": false, "Path": "gces-dashboard/frontend/src/smartAnalyzer/i18n/locals/pl.json"}, {"Module": "Dashboard", "DisplayName": "Admin Portal", "Order": 203, "Obsolete": false, "Path": "gces-dashboard/frontend/src/adminPortal/common/localization/pl.json"}, {"Module": "Reporting Components", "DisplayName": "Doc Permissions Tree", "Order": 204, "Obsolete": false, "Path": "gces-reporting/gces-reporting-components/src/PermissionsTree/i18n/pl/permissionsTree.json"}, {"Module": "Admin Portal", "DisplayName": "Printer Management", "Order": 205, "Obsolete": false, "Path": "gces-server/src/Gces.Server/Apps/Portals/Common/Register/Printer/localization/pl.json"}, {"Module": "Data Model", "DisplayName": "Data Model Designer <PERSON><PERSON><PERSON>", "Order": 206, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/localization/error.pl.json"}, {"Module": "Data Model", "DisplayName": "Data Model Designer", "Order": 207, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/localization/model.pl.json"}, {"Module": "Data Model", "DisplayName": "Data Model Designer2", "Order": 208, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/localization/wizard.pl.json"}, {"Module": "Data Model", "DisplayName": "Data Model Cache Management", "Order": 209, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/CacheManagement/localization/pl.ts"}, {"Module": "Data Model", "DisplayName": "API Error", "Order": 210, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisDesigner/Common/localization/apiError/pl.json"}, {"Module": "Data Model", "DisplayName": "Data Model Preview", "Order": 211, "Obsolete": false, "Path": "gces-analysis/src/Gces.AnalysisModel.Plugin/ClientApp/AnalysisPreview/localization/pl.json"}, {"Module": "Dataset", "DisplayName": "AI Settings", "Order": 212, "Obsolete": false, "Path": "gces-common/src/Gces.Dataset.Plugin/ClientApp/Common/localization/common/AISettings/pl.json"}]