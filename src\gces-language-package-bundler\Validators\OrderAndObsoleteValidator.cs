﻿using System.Collections.Generic;
using System.Linq;

namespace gces_language_package_bundler
{
	public class OrderAndObsoleteValidationRes
	{
		public string Lng { get; set; }
		public List<ResourceFile> InvalidOrderValItems { get; set; } = new List<ResourceFile>();
		public List<ResourceFile> MultiOrdersItems { get; set; } = new List<ResourceFile>();
		public List<ResourceFile> MultiObsoleteItems { get; set; } = new List<ResourceFile>();
		public List<IGrouping<int?, ResourceFile>> OrderDuplicatedItems { get; set; } = new List<IGrouping<int?, ResourceFile>>();
	}

	internal class OrderAndObsoleteValidator
	{
		private const int MinValidOrderValue = 1;
		private const int MaxValidOrderValue = 9999;

		internal static OrderAndObsoleteValidationRes ValidateResourceFileOrderAndObsolete(ResourceItem resourceFilesInSingleLng)
		{
			var res = new OrderAndObsoleteValidationRes
			{
				Lng = resourceFilesInSingleLng.Lng
			};
			var noOrderItems = resourceFilesInSingleLng.Files.Where(f => f.Order == null || f.Order < MinValidOrderValue || f.Order > MaxValidOrderValue).ToList();
			if (noOrderItems.Count() > 0) { res.InvalidOrderValItems.AddRange(noOrderItems); }
			var filesHasOrders = resourceFilesInSingleLng.Files.Where(f => f.Order != null);
			var multipleOrderItems = filesHasOrders.GroupBy(f => f.Name).Where(group => group.Count() > 1)
								  .SelectMany(group => group.Where(file => group.Any(other => other.Order != file.Order)))
								  .ToList();
			res.MultiOrdersItems.AddRange(multipleOrderItems);
			var duplicatedOrderfiles = filesHasOrders.GroupBy(f => f.Order).Where(group => group.Count() > 1 && group.Select(file => file.Name).Distinct().Count() > 1).ToList();
			res.OrderDuplicatedItems.AddRange(duplicatedOrderfiles);

			var multipleObsoleteItems = filesHasOrders.GroupBy(f => f.Name).Where(group => group.Count() > 1)
								  .SelectMany(group => group.Where(file => group.Any(other => other.Obsolete != file.Obsolete)))
								  .ToList();
			res.MultiObsoleteItems.AddRange(multipleOrderItems);

			return res;
		}

	}
}
