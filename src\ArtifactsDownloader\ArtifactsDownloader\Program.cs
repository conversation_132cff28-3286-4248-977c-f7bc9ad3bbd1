﻿using CommandLine;

namespace ArtifactsDownloader
{
	internal class Program
	{
		static int Main(string[] args)
		{
			var downloadOptions = Parser.Default.ParseArguments<DownloadOptions>(args);
			
			if (downloadOptions.Tag == ParserResultType.Parsed)
			{
				var options = downloadOptions.Value;
				var downloader = new Downloader(options);

				if (options.AutoDetectVersion)
				{
					List<(string, string, string)> latestVersions = downloader.GetLatestVersion(options.VersionDetectFilePath);
					if (latestVersions != null)
					{
						string[] lines = latestVersions.Select<(string, string, string), string>(((string repoName, string version, string artifactName) v) => $"{v.repoName,-30} {v.version,-20} {v.artifactName}").ToArray();
						File.WriteAllLines(options.ManifestFilePath, lines);
					}
				}

				return (!downloader.Download()) ? 1 : 0;
			}
			else
			{
				return 0;
			}
		}
	}
}
