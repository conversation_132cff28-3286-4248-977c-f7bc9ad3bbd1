#!/bin/bash

if [ ! -f "/var/monetdb5/dbfarm/.merovingian_properties" ]; then
  echo "Creating dbfarm in 'var/monetdb5/dbfarm'"
  monetdbd create /var/monetdb5/dbfarm
else
  echo "Existing dbfarm found in '/var/monetdb5/dbfarm'"
fi

monetdbd set listenaddr=0.0.0.0 /var/monetdb5/dbfarm
monetdbd set port=54321 /var/monetdb5/dbfarm
monetdbd start /var/monetdb5/dbfarm

sleep 5
if [ ! -d "/var/monetdb5/dbfarm/wyndw" ]; then
  echo "Creating database in '/var/monetdb5/dbfarm/wyndw'"
  monetdb create wyndw
  monetdb set nclients=200 wyndw
  monetdb release wyndw
else
  echo "Existing database found in '/var/monetdb5/dbfarm/wyndw'"
fi

echo "user=monetdb" > .monetdb
echo "password=monetdb" >> .monetdb

for i in {30..0}
do
  echo 'Testing MonetDB connection ' $i
  mclient -d wyndw -s 'SELECT 1' &> /dev/null
  if [ $? -ne 0 ] ; then
    echo 'Waiting for MonetDB to start...'
    sleep 1
  else
    echo 'MonetDB is running'
    break
  fi
done

rm -f .monetdb

if [ $i -eq 0 ]; then
  echo >&2 'MonetDB startup failed'
  exit 1
fi

monetdbd stop /var/monetdb5/dbfarm

sleep 5

monetdbd start -n /var/monetdb5/dbfarm