#!/bin/bash

POSTGRES_INSTALL_DIR="/opt/postgresql"
POSTGRES_DATA_DIR="$POSTGRES_INSTALL_DIR/data"
POSTGRES_PGCTL="$POSTGRES_INSTALL_DIR/pgsql/bin/pg_ctl"
POSTGRES_PORT=5444
POSTGRES_USER=wyn-enterprise

do_start()
{
  sudo -u $POSTGRES_USER $POSTGRES_PGCTL -o "-p $POSTGRES_PORT" -D $POSTGRES_DATA_DIR start
}

do_stop()
{
  sudo -u $POSTGRES_USER $POSTGRES_PGCTL -o "-p $POSTGRES_PORT" -D $POSTGRES_DATA_DIR stop
}

do_restart()
{
  sudo -u $POSTGRES_USER $POSTGRES_PGCTL -o "-p $POSTGRES_PORT" -D $POSTGRES_DATA_DIR restart
}

do_status()
{
  sudo -u $POSTGRES_USER $POSTGRES_PGCTL -D $POSTGRES_DATA_DIR status
}

case "$1" in
  start)
    do_start
    ;;

  stop)
    do_stop
    ;;

  restart)
    do_restart
    ;;

  status)
    do_status
    ;;

  *)
    echo "Usage: $0 {start|stop|restart|status}"
    exit 0
    ;;

esac