#!/bin/bash

if [ "$1" == "abort-upgrade" ]; then
  exit 1
fi

INSTALL_SERVER=false
INSTALL_REPORTING_WORKER=false
CLUSTER_MODE=false

# installation
if [ "$1" == "configure" ]; then
  # upgrade
  if [ -f "/opt/Wyn/.upgrade" ]; then
    if [ -f "/opt/Wyn/Monitor/conf/Wyn.conf" ]; then
      CLUSTER_MODE=true
    fi
    PREVIOUS_VERSION=$(cat "/opt/Wyn/.upgrade")
    if [ -f "/opt/Wyn/.modules" ]; then
      Modules="$(<"/opt/Wyn/.modules")"
      # GEF-10212
      if [ "" == "$Modules" ]; then
        Modules="Server,ReportingWorker,CotWorker,DashboardWorker"
        sudo echo "$Modules" > "/opt/Wyn/.modules"
      fi
      UpperModules="${Modules^^}"
      if [[ "$UpperModules" != *"SERVER"* ]]; then
        sudo rm -rf "/opt/Wyn/Server" > /dev/null 2>/dev/null
        sudo rm -rf "/opt/Wyn/Plugins" > /dev/null 2>/dev/null
        sudo rm -rf "/opt/Wyn/sampledata" > /dev/null 2>/dev/null
        sudo rm -rf "/opt/Wyn/DataSourceService" > /dev/null 2>/dev/null
        sudo rm -rf "/opt/Wyn/MemoryDBService" > /dev/null 2>/dev/null
        sudo rm -rf "/opt/Wyn/SchedulerService" > /dev/null 2>/dev/null
        INSTALL_SERVER=false
      else
        INSTALL_SERVER=true
      fi
      if [[ "$UpperModules" != *"REPORTINGWORKER"* ]]; then
        sudo rm -rf "/opt/Wyn/ReportingWorker" > /dev/null 2>/dev/null
        INSTALL_REPORTING_WORKER=false
      else
        INSTALL_REPORTING_WORKER=true
      fi
      # no extra operations on cot worker
      if [[ "$UpperModules" != *"COTWORKER"* ]]; then
        sudo rm -rf "/opt/Wyn/CotWorker" > /dev/null 2>/dev/null
      fi
      if [[ "$UpperModules" != *"DASHBOARDWORKER"* ]]; then
        sudo rm -rf "/opt/Wyn/DashboardWorker" > /dev/null 2>/dev/null
      fi
    else
      sudo echo "Server,ReportingWorker,CotWorker,DashboardWorker" > "/opt/Wyn/.modules"
      INSTALL_SERVER=true
      INSTALL_REPORTING_WORKER=true
      systemctl --version > /dev/null 2>/dev/null
      if [ "0" == "$?" ]; then
        if [ -d "/lib/systemd/system" ]; then
          if $CLUSTER_MODE ; then
            sudo cp "/opt/Wyn/services/wyn.service" "/lib/systemd/system/"
          else
            sudo cp "/opt/Wyn/services/wyn-light.service" "/lib/systemd/system/wyn.service"
          fi
          sudo systemctl enable wyn.service
        else
          if $CLUSTER_MODE ; then
            sudo cp "/opt/Wyn/services/wyn.service" "/usr/lib/systemd/system/"
          else
            sudo cp "/opt/Wyn/services/wyn-light.service" "/usr/lib/systemd/system/wyn.service"
          fi
          sudo systemctl enable wyn.service
        fi
      else
        sudo cp "/opt/Wyn/services/wyn" "/etc/init.d/"
        sudo chmod 777 "/etc/init.d/wyn"
        sudo update-rc.d wyn 20
      fi
    fi

    # copy resources
    if $INSTALL_REPORTING_WORKER && [ ! -d "/usr/share/fonts/liberation-fonts-ttf-2.00.1" ] ; then
      sudo mkdir -p "/usr/share/fonts"
      sudo tar zxf "/opt/Wyn/liberation-fonts-ttf-2.00.1.tar.gz" -C "/usr/share/fonts" > /dev/null 2>/dev/null
    fi
    
    # install MonetDB
    if $INSTALL_SERVER ; then
      needUninstallMonetDB=false
      needIntallMonetDB=false
      monetdbd --version > /dev/null 2>&1
      if [ "0" == "$?" ]; then
        # MonetDB Database Server v11.37.11 (Jun2020-SP1)
        targetVersion="11.43.23"
        text=$(monetdbd --version)
        version=$(echo "$text" | cut -c 26-33)
        currentVersion=$(echo "${version//[[:space:]]/}")
        for idx in 1 2 3
        do
          v1=$(echo "$currentVersion" | cut -d '.' -f $idx)
          v2=$(echo "$targetVersion" | cut -d '.' -f $idx)
          if [ "$v1" -lt "$v2" ]; then
            needUninstallMonetDB=true
            needIntallMonetDB=true
            break
          fi
        done
      elif $CLUSTER_MODE ; then
        needIntallMonetDB=true
      fi
      
      if $needUninstallMonetDB ; then
        # remove monetdb 11.37.11
        if [ -f "/lib/systemd/system/monetdbd.service" ]; then
          sudo rm "/lib/systemd/system/monetdbd.service"
        fi
        if [ -f "/usr/lib/systemd/system/monetdbd.service" ]; then
          sudo rm "/usr/lib/systemd/system/monetdbd.service"
        fi
        if [ -L "/etc/systemd/system/multi-user.target.wants/monetdbd.service" ]; then
          sudo rm "/etc/systemd/system/multi-user.target.wants/monetdbd.service"
        fi
        sudo rm -rf /etc/default/monetdb5-sql > /dev/null 2>&1
        sudo rm -rf /etc/init.d/monetdb5-sql > /dev/null 2>&1
        sudo rm -rf /etc/logrotate.d/monetdb > /dev/null 2>&1
        sudo rm -rf /run/monetdb > /dev/null 2>&1
        sudo rm -rf /usr/bin/mclient > /dev/null 2>&1
        sudo rm -rf /usr/bin/monetdb > /dev/null 2>&1
        sudo rm -rf /usr/bin/monetdbd > /dev/null 2>&1
        sudo rm -rf /usr/bin/mserver5 > /dev/null 2>&1
        sudo rm -rf /usr/bin/msqldump > /dev/null 2>&1
        sudo rm -rf /usr/lib/x86_64-linux-gnu/monetdb5 > /dev/null 2>&1
        sudo rm -rf /usr/lib/x86_64-linux-gnu/libmapi.* > /dev/null 2>&1
        sudo rm -rf /usr/lib/x86_64-linux-gnu/libmonetdb.* > /dev/null 2>&1
        sudo rm -rf /usr/share/doc/libmonetdb* > /dev/null 2>&1
        sudo rm -rf /usr/share/doc/monetdb* > /dev/null 2>&1
        sudo rm -rf /usr/share/man/man1/mclient.1.gz > /dev/null 2>&1
        sudo rm -rf /usr/share/man/man1/monetdb.1.gz > /dev/null 2>&1
        sudo rm -rf /usr/share/man/man1/monetdbd.1.gz > /dev/null 2>&1
        sudo rm -rf /usr/share/man/man1/mserver5.1.gz > /dev/null 2>&1
        sudo rm -rf /usr/share/man/man1/msqldump.1.gz > /dev/null 2>&1
        sudo rm -rf /var/lib/monetdb > /dev/null 2>&1
        sudo rm -rf /var/log/monetdb > /dev/null 2>&1
      fi
      if $needIntallMonetDB ; then
        echo "Installing MonetDB..."
        rm -rf /tmp/monetdb > /dev/null 2>/dev/null
        tar zxf /opt/Wyn/monetdb-11.43.23.tar.gz -C /tmp/
        text=$(cat /etc/os-release | grep UBUNTU_CODENAME)
        codename="${text//UBUNTU_CODENAME=/}"
        if [ -d "/tmp/monetdb/ubuntu/$codename" ]; then
          cd "/tmp/monetdb/ubuntu/$codename"
          sudo bash ./monetdb-install.sh
          if [ "0" == "$?" ]; then
            echo "MonetDB installed successfully."
          else
            echo "Install MonetDB failed."
          fi
          cd - > /dev/null 2>/dev/null
        else
          echo "Won't install MonetDB on this server since unsupported Ubuntu version detected, the codename is '$codename'."
        fi
      fi
    fi
    
    if $INSTALL_SERVER && $CLUSTER_MODE && [ ! -d "/opt/Wyn/jre" ] ; then
      sudo tar zxf "/opt/Wyn/jre8.tar.gz" -C "/opt/Wyn/" > /dev/null 2>/dev/null
    fi
    
    # backup configuration file
    if [ -f "/opt/Wyn/Monitor/conf/Wyn.conf" ]; then
      sudo cp "/opt/Wyn/Monitor/conf/Wyn.conf" "/opt/Wyn/Monitor/conf/Wyn.conf.$(date +"%Y%m%d%H%M%S")"
    elif [ -f "/opt/Wyn/conf/Wyn.conf" ]; then
      sudo cp "/opt/Wyn/conf/Wyn.conf" "/opt/Wyn/conf/Wyn.conf.$(date +"%Y%m%d%H%M%S")"
    fi
    
    
    if ! $CLUSTER_MODE ; then
      sudo rm -rf "/opt/Wyn/monetdb" > /dev/null 2>&1
      sudo rm -rf "/opt/Wyn/Monitor" > /dev/null 2>&1
      sudo rm -rf "/opt/Wyn/MonitorUpdater" > /dev/null 2>&1
    fi
    
    # starting service
    sudo echo "Starting service wyn.service..."
    systemctl --version > /dev/null 2>/dev/null
    if [ "0" == "$?" ]; then
      sudo systemctl daemon-reload
      sudo systemctl start wyn.service
      sudo systemctl start wyn-datasource.service > /dev/null 2>/dev/null
    else
      sudo service wyn start
    fi
    
    # remove useless files
    sudo rm "/opt/Wyn/.upgrade" > /dev/null 2>/dev/null
    sudo rm -rf "/opt/Wyn/postgresql-10.4-1-linux-x64-binaries.tar.gz" > /dev/null 2>/dev/null
    sudo rm -rf "/opt/Wyn/jre8.tar.gz" > /dev/null 2>/dev/null
    sudo rm -rf "/opt/Wyn/services" > /dev/null 2>/dev/null
    sudo rm "/opt/Wyn/liberation-fonts-ttf-2.00.1.tar.gz" > /dev/null 2>/dev/null
    sudo rm "/opt/Wyn/monetdb-11.43.23.tar.gz" > /dev/null 2>/dev/null
    sudo rm -rf "/opt/Wyn/monetdb" > /dev/null 2>&1
    sudo rm -rf "/tmp/monetdb" > /dev/null 2>/dev/null
    
    # wait for service startup
    sleep 30
    sudo echo "Finished."
  else
    echo -e "\e[32mAfter the installation, you should run a script to configure the application using command 'sudo bash /opt/Wyn/Monitor/configure.sh'.\e[0m"
  fi
fi

exit 0