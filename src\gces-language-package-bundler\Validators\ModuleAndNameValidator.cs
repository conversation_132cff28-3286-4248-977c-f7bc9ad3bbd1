﻿using System.Collections.Generic;
using System.Linq;

namespace gces_language_package_bundler
{
	internal class ModuleAndNameValidator
	{
		internal static Dictionary<string, IGrouping<string, ResourceFile>> GetDulicatedModuleAndDisplayItems(Dictionary<string, List<ResourceFile>> resourceNameDict)
		{
			var duplicateItems = new Dictionary<string, IGrouping<string, ResourceFile>>();
			foreach (var resourceKeyVal in resourceNameDict)
			{
				var moduleAndNameDuplicateItems = resourceKeyVal.Value.GroupBy(s => s.Module + ":" + s.DisplayName).Where(g => g.Count() > 1).ToList();

				foreach (var duplicateItem in moduleAndNameDuplicateItems)
				{
					var duplicatedPaths = duplicateItem.Select(d => d.Name).Distinct();
					if (duplicatedPaths.Count() > 1)
					{
						duplicateItems.TryAdd(resourceKeyVal.Key, duplicateItem);
					}
				}
			}

			return duplicateItems;
		}
	}
}
