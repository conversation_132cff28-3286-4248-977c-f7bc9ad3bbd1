﻿using System;
using System.Collections.Generic;

namespace gces_artifacts_downloader
{
	public class SelfLink
	{
		public string href { get; set; }
	}

	public class LinkInfo
	{
		public SelfLink self { get; set; }
	}

	public class ArtifactInfo
	{
		public string name { get; set; }
		public LinkInfo links { get; set; }
		public int downloads { get; set; }
		public DateTime created_on { get; set; }
		public string type { get; set; }
		public int size { get; set; }
	}

	public class ArtifactsCollection
	{
		public int pagelen { get; set; }
		public int size { get; set; }
		public List<ArtifactInfo> values { get; set; }
		public int page { get; set; }
		public string next { get; set; }
	}
}
