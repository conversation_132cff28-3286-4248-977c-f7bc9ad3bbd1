#!/bin/bash
# $1 - image name
# $2 - version number
# $3 - content folder
# $4 - dependencies folder
# $5 - docker repository
# $6 - docker account
# $7 - docker password

echo "check parameters..."
if [ "" == "$1" ]; then
  echo "empty image name detected."
  exit 1
fi
if [ "" == "$2" ]; then
  echo "empty version number detected."
  exit 1
fi
if [[ "$2" != "9.0.00"* ]]; then
  echo "invalid version number $2 detected."
  exit 1
else
  echo "version number: $2"
fi

ImageName="$1"
ImageVersion="$2"
ContentFolder="$3"
DepenencyFolder="$4"
#DockerRepo="gcescr.azurecr.io"
DockerRepo="$5"
DockerAccount="$6"
DockerPassword="$7"

IsK8s=false
if [[ "$ImageName" == *"-service-runner"* || "$ImageName" == *"-k8s"* ]]; then
  IsK8s=true
fi

IsArm64=false
if [[ "$ImageName" == *"-arm64"* ]]; then
  IsArm64=true
fi

echo "parameters:"
echo "  ImageName=$ImageName"
echo "  ImageVersion=$ImageVersion"
echo "  IsK8s=$IsK8s"
echo "  IsArm64=$IsArm64"

WynDir=./wyn

echo "prepare files..."
rm -rf $WynDir > /dev/null 2>&1
mkdir -p $WynDir
cp -fr $ContentFolder/* $WynDir/
shopt -s globstar
rm -rf ./**/runtimes/browser*
rm -rf ./**/runtimes/macc*
rm -rf ./**/runtimes/android*
rm -rf ./**/runtimes/solaris*
rm -rf ./**/runtimes/tvos*
rm -rf ./**/runtimes/freebsd*
rm -rf ./**/runtimes/illumos*
rm -rf ./**/runtimes/ios*
rm -rf ./**/runtimes/osx*
rm -rf ./**/runtimes/alpine*
rm -rf ./**/runtimes/linux-mips*
rm -rf ./**/runtimes/linux-musl*
rm -rf ./**/runtimes/linux-s390*
rm -rf ./**/runtimes/linux-ppc64*
rm -rf ./**/runtimes/win*
if ! $IsArm64 ; then
  rm -rf ./**/runtimes/linux-arm*
fi
if $IsK8s ; then
  rm -rf $WynDir/Monitor
  mv $WynDir/Server/wwwroot/map $WynDir/Server/wwwroot/builtin-map
  mv $WynDir/Server/SecurityProviders $WynDir/Server/builtin-sps
else
  rm -rf $WynDir/Monitor/conf/Wyn.conf
  rm -rf $WynDir/ServiceRunner
fi

tar -zxf $DepenencyFolder/liberation-fonts-ttf-2.00.1.tar.gz -C ./

if $IsArm64 ; then
  tar -zxf $DepenencyFolder/jre8-arm64.tar.gz -C ./
else
  tar -zxf $DepenencyFolder/jre8.tar.gz -C ./
fi

docker rmi $ImageName > /dev/null 2>&1

echo "pull docker image..."
if $IsArm64 ; then
  docker pull $DockerRepo/gces-arm64-8.0
  echo "build arm64 image..."
  docker buildx build --no-cache --platform linux/arm64 -t $ImageName -o type=docker -f dockerfile.arm64 .
elif $IsK8s ; then
  docker pull $DockerRepo/gces-base-amd64-8.0
  echo "build k8s image..."
  docker build --no-cache -t $ImageName -f service-runner-dockerfile .
else
  docker pull $DockerRepo/gces-amd64-8.0
  echo "build x86 image..."
  docker build --no-cache -t $ImageName -f dockerfile .
fi

docker login -u $DockerAccount -p $DockerPassword $DockerRepo

echo "tag docker image..."
docker tag $ImageName $DockerRepo/$ImageName:$ImageVersion
docker tag $ImageName $DockerRepo/$ImageName:latest

echo "push docker image"
docker push $DockerRepo/$ImageName:$ImageVersion
docker push $DockerRepo/$ImageName:latest

echo "cleanup docker image..."
docker rmi $DockerRepo/$ImageName:$ImageVersion
docker rmi $DockerRepo/$ImageName:latest

echo "cleanup docker builder cache..."
docker builder prune -af

rm -rf $WynDir > /dev/null 2>&1
echo "finished."
exit 0
