{"version": "0.2.0", "configurations": [{"name": "ArtifactsDownloader", "type": "coreclr", "request": "launch", "preLaunchTask": "dotnet: build", "cwd": "${workspaceFolder}/ArtifactsDownloader/bin/Debug/netcoreapp6.0", "program": "ArtifactsDownloader.dll", "args": ["-m", "manifest.txt", "-o", "output", "-a", "-v", "version-detect.txt", "-e", "Dependencies_EN_8.1", "-c", "https://wynintdelivery.blob.core.windows.net/wynintartifacts?sv=2023-01-03&st=2024-11-06T10%3A31%3A54Z&se=2025-08-22T08%3A15%3A00Z&sr=c&sp=racwdxltf&sig=25MEjYEcxWfSS5kejElU6V34WYvIS9%2B5DSBdDg%2BJqeg%3D"], "pipeTransport": {"pipeProgram": "cmd", "pipeArgs": ["/c"], "debuggerPath": "C:\\tools\\netcoredbg\\netcoredbg.exe"}}]}