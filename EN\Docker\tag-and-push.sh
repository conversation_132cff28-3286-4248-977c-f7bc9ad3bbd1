#!/bin/bash
# $1 - source image name
# $2 - target image name
# $3 - image version number
# $4 - source docker registry
# $5 - source docker repository
# $6 - source docker account
# $7 - source docker password
# $8 - target docker registry
# $9 - target docker repository
# $10 - target docker account
# $11 - target docker password

echo "check parameters..."
if [ "" == "$1" ]; then
  echo "empty source image name detected."
  exit 1
fi
if [ "" == "$2" ]; then
  echo "empty target image name detected."
  exit 1
fi
if [ "" == "$3" ]; then
  echo "empty image version number detected."
  exit 1
fi
if [[ "$3" != "9.0.00"* ]]; then
  echo "invalid image version number $3 detected."
  exit 1
else
  echo "image version number: $3"
fi

SourceImageName="$1"
TargetImageName="$2"
ImageVersion="$3"

# gcescr.azurecr.io
SourceDockerRegistry="$4"
# grapecity
SourceDockerRepository="$5"
SourceDockerAccount="$6"
SourceDockerPassword="$7"
# wyndocker.azurecr.io
TargetDockerRegistry="$8"
# wynenterprise
TargetDockerRepository="$9"
TargetDockerAccount="$10"
TargetDockerPassword="$11"

SourceImageFullName=""
TargetImageFullName=""
if [ "" == "$SourceDockerRegistry" ]; then
  if [ "" == "$SourceDockerRepository" ]; then
    SourceImageFullName="$SourceDockerAccount/$SourceImageName"
  else
    SourceImageFullName="$SourceDockerRepository/$SourceImageName"
  fi
else
  if [ "" == "$SourceDockerRepository" ]; then
    SourceImageFullName="$SourceDockerRegistry/$SourceImageName"
  else
    SourceImageFullName="$SourceDockerRegistry/$SourceDockerRepository/$SourceImageName"
  fi
fi
if [ "" == "$TargetDockerRegistry" ]; then
  if [ "" == "$TargetDockerRepository" ]; then
    TargetImageFullName="$TargetDockerAccount/$TargetImageName"
  else
    TargetImageFullName="$TargetDockerRepository/$TargetImageName"
  fi
else
  if [ "" == "$TargetDockerRepository" ]; then
    TargetImageFullName="$TargetDockerRegistry/$TargetImageName"
  else
    TargetImageFullName="$TargetDockerRegistry/$TargetDockerRepository/$TargetImageName"
  fi
fi

echo "pull docker image..."
if [ "" == "$SourceDockerRegistry" ]; then
  docker login -u $SourceDockerAccount -p $SourceDockerPassword
  docker pull $SourceImageFullName:$ImageVersion
else
  docker login -u $SourceDockerAccount -p $SourceDockerPassword $SourceDockerRegistry
  docker pull $SourceImageFullName:$ImageVersion
fi

echo "tag docker image..."
docker tag $SourceImageFullName:$ImageVersion $TargetImageFullName:$ImageVersion
docker tag $SourceImageFullName:$ImageVersion $TargetImageFullName:latest

if [ "" == "$TargetDockerRegistry" ]; then
  docker login -u $TargetDockerAccount -p $TargetDockerPassword
else
  docker login -u $TargetDockerAccount -p $TargetDockerPassword $TargetDockerRegistry
fi

echo "push docker image"
docker push $TargetImageFullName:$ImageVersion
docker push $TargetImageFullName:latest

echo "cleanup docker image..."
docker rmi $TargetImageFullName:$ImageVersion
docker rmi $TargetImageFullName:latest
docker rmi $SourceImageFullName:$ImageVersion

echo "cleanup docker builder cache..."
docker builder prune -af

echo "finished."
exit 0
