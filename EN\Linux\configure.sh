#!/bin/bash

#====================================== GLOBAL VARIABLES ======================================#

INSTALL_TARGET_DIR="/opt"
WYN_INSTALL_DIR="$INSTALL_TARGET_DIR/Wyn"
SERVICES_FOLDER="$WYN_INSTALL_DIR/services"

CLUSTER_MODE=false

DB_PROVIDER_POSTGRES="Postgres"
DB_PROVIDER_SQLSERVER="SqlServer"
DB_PROVIDER_MYSQL="MySql"
DB_PROVIDER_ORACLE="Oracle"
DB_PROVIDER_SQLITE="SQLite"

DB_PROVIDER=""
DB_HOST=""
DB_PORT=0
DB_USERNAME=""
DB_PASSWORD=""
ORACLE_SERVICE_NAME=""
DATABASE_CONNECTIONSTRING=""
USE_EXISTING_DATABASE=false
SINGLE_DATABASE_MODE=false
ENCRYPT_DATABASE_CONNECTION_STRING=false
LNG_ENGLISH="English"
LNG_ENGLISH_CODE="en-US"
LNG_KOREAN="Korean"
LNG_KOREAN_CODE="ko-KR"
DEFAULT_LANGUAGE="$LNG_ENGLISH"

MODULE_SERVER="Server"
MODULE_REPORTING_WORKER="ReportingWorker"
MODULE_COT_WORKER="CotWorker"
MODULE_DASHBOARD_WORKER="DashboardWorker"
MODULE_MONITOR="Monitor"
declare -A MODULES
MODULES_FILE_NAME=".modules"
MODULES_FILE="$WYN_INSTALL_DIR/$MODULES_FILE_NAME"
NEED_INSTALL_SERVER=false
NEED_INSTALL_REPORTING_WORKER=false
NEED_INSTALL_COT_WORKER=false
NEED_INSTALL_DASHBOARD_WORKER=false

IMPORT_SAMPLES=false
SAMPLE_FILE=""

CONFIG_FILE_NAME="Wyn.conf"
CONFIG_FILE_PATH="$WYN_INSTALL_DIR/conf/$CONFIG_FILE_NAME"
CONFIG_FILE_EXISTS=false

POSTGRES_INSTALL_DIR="$INSTALL_TARGET_DIR/postgresql"
POSTGRES_WYN_DATA_DIR="$POSTGRES_INSTALL_DIR/data"
POSTGRES_INSTALLER_NAME="postgresql-10.4-1-linux-x64-binaries.tar.gz"
POSTGRES_ADMIN_NAME="wyn-enterprise"
POSTGRES_ADMIN_PASSWORD="Wr8TGfe2r0"
POSTGRES_LISTENING_PORT=5444
POSTGRES_INITDB="$POSTGRES_INSTALL_DIR/pgsql/bin/initdb"
POSTGRES_PGCTL="$POSTGRES_INSTALL_DIR/pgsql/bin/pg_ctl"
POSTGRES_POSTGRES="$POSTGRES_INSTALL_DIR/pgsql/bin/postgres"
POSTGRES_AUTHMODE="md5"
POSTGRES_FILE="$WYN_INSTALL_DIR/$POSTGRES_INSTALLER_NAME"
POSTGRES_PWFILE="$POSTGRES_INSTALL_DIR/.pwf"

JRE8_FILE_NAME="jre8.tar.gz"
MONETDB_FILE="$WYN_INSTALL_DIR/monetdb-11.43.23.tar.gz"

WYN_SERVER_PORT=51980
WYN_SCHEDULER_SERVICE_PORT=51981
WYN_REPORTING_WORKER_PORT=51982
WYN_COT_WORKER_PORT=51983
WYN_DASHBOARD_WORKER_PORT=51984
WYN_MONITOR_PORT=51986
WYN_MEMORY_DB_SERVICE_PORT=51987
WYN_DATA_SOURCE_SERVICE_PORT=51988

SYSTEMCTL="systemctl"
SERVICE_MANAGE_COMMAND="$SYSTEMCTL"
SERVICE_FILE_LOCATION="/lib/systemd/system"
WYN_DATABASE_SERVICE="wyn-database.service"
WYN_DATABASE_SERVICE_SEQUENCE=10
WYN_SERVICE="wyn.service"
WYN_SERVICE_SEQUENCE=20
WYN_LIGHT_SERVICE="wyn-light.service"
PKG_MGMT_CMD="apt"

SILENT_INSTALLATION=false
LOG_FILE="install.log"
LICENSE=""
ADMIN_PASSWORD="admin"
CREDENTIAL_FILE="$WYN_INSTALL_DIR/$MODULE_SERVER/credential"
WYN_VERSION=""

HOST_IP_ADDRESS="localhost"
SERVER_URI="http://$HOST_IP_ADDRESS:$WYN_SERVER_PORT"
BACKEND_SERVER_ADDRESS=""
SECONDARY_NODE_SECRET=""

FONT_FILE="$WYN_INSTALL_DIR/liberation-fonts-ttf-2.00.1.tar.gz"

EXIT_CODE_SUCCESS=0
EXIT_CODE_SHOW_HELP=1
EXIT_CODE_INVALID_ARGUMENTS=2
EXIT_CODE_INVALID_DATABASE_PROVIDER=3
EXIT_CODE_INVALID_DATABASE_CONNECTION_STRING=4
EXIT_CODE_INVALID_MYSQL_REQUIREMENT=5
EXIT_CODE_INVALID_MODULES_DETECTED=6
EXIT_CODE_NO_MODULES_SELECTED=7
EXIT_CODE_NO_BACKEND_SERVER_ADDRESS=8
EXIT_CODE_START_WYN_FAILED=9
EXIT_CODE_REGISTER_LICENSE_FAILED=10
EXIT_CODE_INVALID_PASSWORD=11
EXIT_CODE_UNSUPPORTED_POSTGRES_VERSION=12
EXIT_CODE_CTRL_C=100

#====================================== FUNCTIONS ======================================#

prtMsg() {
  for msg in "$@"
  do
    if $SILENT_INSTALLATION ; then
      echo "$(date) $msg" >> $LOG_FILE
    else
      echo "$msg"
    fi
  done
}

# catch "ctrl-c" and exit the installation
trap_ctrlc() {
  prtMsg ""
  prtMsg "Ctrl-C caught, canceling the installation..."
  stty echo
  exit $EXIT_CODE_CTRL_C
}

parseArguments() {
  OPTIND=1
  local ret=0
  while getopts sd:c:om:if:b:k:a:v:t:L:elh opt; do
    case $opt in
      s)
        SILENT_INSTALLATION=true
        ;;
      d)
        DB_PROVIDER="$OPTARG"
        ;;
      c)
        DATABASE_CONNECTIONSTRING="$OPTARG"
        ;;
      o)
        SINGLE_DATABASE_MODE=true
        ;;
      m)
        parseModules "$OPTARG"
        ;;
      i)
        IMPORT_SAMPLES=true
        ;;
      f)
        SAMPLE_FILE="$OPTARG"
        ;;
      b)
        BACKEND_SERVER_ADDRESS="$OPTARG"
        ;;
      k)
        LICENSE="$OPTARG"
        ;;
      a)
        ADMIN_PASSWORD="$OPTARG"
        ;;
      v)
        WYN_VERSION="$OPTARG"
        ;;
      t)
        SECONDARY_NODE_SECRET="$OPTARG"
        ;;
      L)
        DEFAULT_LANGUAGE="$OPTARG"
        ;;
      e)
        ENCRYPT_DATABASE_CONNECTION_STRING=true
        ;;
      l)
        CLUSTER_MODE=true
        CONFIG_FILE_PATH="$WYN_INSTALL_DIR/$MODULE_MONITOR/conf/$CONFIG_FILE_NAME"
        ;;
      h)
        ret=$EXIT_CODE_SHOW_HELP
        ;;
      *)
        ret=$EXIT_CODE_INVALID_ARGUMENTS
        ;;
    esac
  done
  if ! $CLUSTER_MODE && [ "" == "$DB_PROVIDER" ] ; then
    DB_PROVIDER="$DB_PROVIDER_SQLITE"
  fi
  return $ret
}

showUsage() {
  prtMsg "Usage: ${0##*/} [-s] -d {DATABASE_PROVIDER} [-c {DATABASE_CONNECTION_STRING}] [-o] [-m {MODULES}] [-i [-f {SAMPLE_FILE}]] [-b {BACKEND_SERVER_ADDRESS}] [-k {LICENSE_KEY}] [-a {ADMIN_PASSWORD}] [-v {VERSION}] [-t {SECONDARY_NODE_SECRET}] [-L {DEFAULT_LANGUAGE}] [-e {ENCRYPT_DATABASE_CONNECTION_STRING}] [-l {NON_SINGLE_PROCESS_MODE}] [-h]
  -s    Silent installation.
  -d    Database provider, available values: 'Postgres', 'SqlServer', 'MySql' and 'Oracle'.
  -c    Database server connection string.
  -o    Single database mode.
  -m    Modules to be installed, including '$MODULE_SERVER', '$MODULE_REPORTING_WORKER', '$MODULE_COT_WORKER' and '$MODULE_DASHBOARD_WORKER'.
  -i    Import samples.
  -f    The location of the user specified sample data.
  -b    The back-end server address.
  -k    The license key to register to Wyn system.
  -a    The password of the administrator user 'admin'.
  -v    The version of Wyn which you want to install on this server.
  -t    The secret for secondary node.
  -L    The default language, available values: '$LNG_ENGLISH_CODE', and '$LNG_KOREAN_CODE'.
  -e    Encrypt the database connection string.
  -l    Non single-process mode.
  -h    Help information."
}

# output: host IP address
getHostIp() {
  local blank=" "
  local ips=$(hostname -I)
  if [ "$ips" == "" ]; then
    echo "localhost"
  else
    local idx=$(expr index "$ips" "$blank")
    if [ "$idx" == "0" ]; then
      echo "$ips"
    else
      idx=$(expr $idx - 1)
      local ip=$(expr substr "$ips" 1 $idx)
      echo "$ip"
    fi
  fi
}

checkSystemInfo() {
  systemctl --version > /dev/null 2>/dev/null
  if [ "0" == "$?" ]; then
    SERVICE_MANAGE_COMMAND="$SYSTEMCTL"
    WYN_SERVICE="wyn.service"
    WYN_DATABASE_SERVICE="wyn-database.service"
    SERVICE_FILE_LOCATION="/lib/systemd/system"
    if [ ! -d "$SERVICE_FILE_LOCATION" ]; then
      SERVICE_FILE_LOCATION="/usr/lib/systemd/system"
    fi
  else
    SERVICE_MANAGE_COMMAND="service"
    WYN_SERVICE="wyn"
    WYN_DATABASE_SERVICE="wyn-database"
    SERVICE_FILE_LOCATION="/etc/init.d"
  fi
  cat /etc/os-release | grep UBUNTU > /dev/null 2>&1
  if [ "0" == "$?" ]; then
    PKG_MGMT_CMD="apt"
  else
    cat /etc/os-release | grep suse > /dev/null 2>&1
    if [ "0" == "$?" ]; then
      PKG_MGMT_CMD="zypper"
    else
      PKG_MGMT_CMD="yum"
    fi
  fi
}

# install font
installFont() {
  local fontsDir="/usr/share/fonts"
  local fontDir="$fontsDir/liberation-fonts-ttf-2.00.1"
  if $NEED_INSTALL_REPORTING_WORKER && [ ! -d "$fontDir" ]; then
    sudo mkdir -p $fontsDir
    sudo tar zxf $FONT_FILE -C $fontsDir > /dev/null 2>/dev/null
  fi
}

# input: $1 - normal text
base64Encode() {
  echo -n "$1" | base64 -w 0
}
# input: $1 - base64 text
base64Decode() {
  echo "$1" | base64 -d
}

# input: $1 - text
# output: text without whitespace
removeWhitespace() {
  echo "${1//[[:space:]]/}"
}
# input: $1 - text
# output: upper text
toUpper() {
  echo "${1^^}"
}
# input: $1 - text
# output lower text
toLower() {
  echo "${1,,}"
}
# input: $1 - source text, $2 - search text, $3 - case sensitive
# return: 0 - contains, otherwise - does not contain
strContains() {
  local sourceText="$1"
  local searchText="$2"
  if [ "0" == "$3" ]; then
    sourceText="$(toUpper "$1")"
    searchText="$(toUpper "$2")"
  fi
  if [[ "$sourceText" == *"$searchText"* ]]; then
    return 0
  else
    return 1
  fi
}
# input: $1 - input text, $2 - pattern, $3 - replace text
# output: replaced text
strReplace() {
  echo "${1//$2/$3}"
}
# input: $1 - delimiter, $2 - arry
# output: joined string
strJoin() {
  local IFS="$1"
  shift
  echo "$*"
}
# input: $1 - input text
# return: text length
strLength() {
  LENGTH=$(echo -n "$1" | wc -m)
  return $LENGTH
}
# input: $1 - origin text, $2 - start index, $3 - end index, '' indicate to the end
# output: sub string
substring() {
  echo "$1" | cut -c $2-$3
}
# input: $1 - the text to check
isEmptyOrWhitespace() {
  if [ "" == "$1" ]; then
    return 0
  else
    if [[ $1 =~ ^\ +$ ]]; then
      return 0
    else
      return 1
    fi
  fi
}
# input: $1 - input text
# output: encoded text
xmlEncode() {
  local text="$(strReplace "$1" '&' '&amp;')"
  text="$(strReplace "$text" "'" '&apos;')"
  text="$(strReplace "$text" '"' '&quot;')"
  text="$(strReplace "$text" '<' '&lt;')"
  text="$(strReplace "$text" '>' '&gt;')"
  echo "$text"
}

# write selected modules to file .modules
# return value: 0 - ok, 1 - cancel
selectModules() {
  sudo rm "$MODULES_FILE_NAME" > /dev/null 2>/dev/null
  local ret=0
  local CMD="whiptail"
  whiptail --version > /dev/null 2>&1
  if [ "0" != "$?" ]; then
    CMD="dialog"
  fi
  while :
  do
    $CMD --title "SELECT MODULES" --clear --checklist \
      '\n   Select the modules you want to install:\n   (Use SPACE bar to toggle the selection)' \
      14 82 4 \
      "$MODULE_SERVER" 'Providing user interface and managing back-end data.' on \
      "$MODULE_REPORTING_WORKER" 'Rendering reports.' on \
      "$MODULE_COT_WORKER" 'Calculating dashboard data.' on \
      "$MODULE_DASHBOARD_WORKER" 'Exporting dashboard to PDF.' on \
      2>$MODULES_FILE_NAME
    ret=$?
    if [ "0" == "$ret" ]; then
      local modules=$(cat "$MODULES_FILE_NAME")
      if [ "" == "$modules" ]; then
        prtMsg "You do not select any module to install, please choose again."
      else
        modules="$(strReplace "$modules" "\"" "")"
        modules="$(strReplace "$modules" " " ",")"
        sudo echo "$modules" > "$MODULES_FILE_NAME"
        break
      fi
    else
      sudo rm "$MODULES_FILE_NAME" > /dev/null 2>&1
      prtMsg "The installation process terminated since the modules selection step was canceled."
      break
    fi
  done
  return $ret
}
# input: $1 - modules
# return: modules count
parseModules() {
  local modules="$(removeWhitespace "$1")"
  IFS=','
  read -ra ARRAY <<< "$modules"
  unset MODULES
  local i=0
  for item in "${ARRAY[@]}"; do
    if [ "" != "$item" ]; then
      MODULES[$i]="$item"
      i=$((i+1))
    fi
  done
  return $i
}
# output: invalid modules
# return: 0 - has invalid module, 1 - does not have invalid module
hasInvalidModules() {
  local upperServer="$(toUpper "$MODULE_SERVER")"
  local upperRptwrk="$(toUpper "$MODULE_REPORTING_WORKER")"
  local upperCotwrk="$(toUpper "$MODULE_COT_WORKER")"
  local upperDbdwrk="$(toUpper "$MODULE_DASHBOARD_WORKER")"
  declare -A invalidModules
  local i=0
  local ret=1
  for item in "${MODULES[@]}"; do
    local upperItem="$(toUpper "$item")"
    if [ "$upperItem" != "$upperServer" -a "$upperItem" != "$upperRptwrk" -a "$upperItem" != "$upperCotwrk" -a "$upperItem" != "$upperDbdwrk" ]; then
      ret=0
      invalidModules[$i]="$item"
      i=$((i+1))
    fi
  done
  echo "$(strJoin ',' "${invalidModules[@]}")"
  return $ret
}
# input: $1 - module name
# return: 0 - need install, otherwise - does not need
needInstallModule() {
  local ret=1
  local upperModule="$(toUpper "$1")"
  for item in "${MODULES[@]}"; do
    local upperItem="$(toUpper "$item")"
    if [ "$upperModule" == "$upperItem" ]; then
      ret=0
      break
    fi
  done
  return $ret
}
writeModulesFile() {
  sudo rm "$MODULES_FILE" > /dev/null 2>/dev/null
  local modules="$(strJoin ',' "${MODULES[@]}")"
  sudo echo "$modules" > "$MODULES_FILE"
}

# input: $1 - service name
stopService() {
  if [ "$SERVICE_MANAGE_COMMAND" == "$SYSTEMCTL" ]; then
    sudo systemctl is-active -q $1
    if [ "0" == "$?" ]; then
      if $SILENT_INSTALLATION ; then
        sudo systemctl stop $1 >> $LOG_FILE 2>&1
      else
        sudo systemctl stop $1
      fi
      sleep 1
    fi
  else
    sudo service $1 status -q > /dev/null 2>/dev/null
    if [ "0" == "$?" ]; then
      if $SILENT_INSTALLATION ; then
        sudo service $1 stop >> $LOG_FILE 2>&1
      else
        sudo service $1 stop
      fi
      sleep 1
    fi
  fi
}
# input: $1 - service name
startService() {
  prtMsg "Starting service $1 ..."
  if [ "$SERVICE_MANAGE_COMMAND" == "$SYSTEMCTL" ]; then
    if $SILENT_INSTALLATION ; then
      sudo systemctl start $1 >> $LOG_FILE 2>&1
    else
      sudo systemctl start $1
    fi
  else
    if $SILENT_INSTALLATION ; then
      sudo service $1 start >> $LOG_FILE 2>&1
    else
      sudo service $1 start
    fi
  fi
}

# input: $1 - service name, $2 - service sequence
createService() {
  if [ ! -f "$SERVICE_FILE_LOCATION/$1" ]; then
    sudo cp "$SERVICES_FOLDER/$1" "$SERVICE_FILE_LOCATION"
    if [ "$SERVICE_MANAGE_COMMAND" == "$SYSTEMCTL" ]; then
      if $SILENT_INSTALLATION ; then
        sudo ln -s "$SERVICE_FILE_LOCATION/$1" "/etc/systemd/system/multi-user.target.wants/$1" >> $LOG_FILE 2>&1
      else
        sudo ln -s "$SERVICE_FILE_LOCATION/$1" "/etc/systemd/system/multi-user.target.wants/$1"
      fi
    else
      if $SILENT_INSTALLATION ; then
        sudo chmod 777 "$SERVICE_FILE_LOCATION/$1" >> $LOG_FILE 2>&1
        sudo update-rc.d $1 defaults $2 >> $LOG_FILE 2>&1
      else
        sudo chmod 777 "$SERVICE_FILE_LOCATION/$1"
        sudo update-rc.d $1 defaults $2
      fi
    fi
  fi
}
removeNeedlessModules() {
  if ! $CLUSTER_MODE ; then
    sudo rm -rf "$WYN_INSTALL_DIR/monetdb" > /dev/null 2>&1
    sudo rm -rf "$WYN_INSTALL_DIR/Monitor" > /dev/null 2>&1
    sudo rm -rf "$WYN_INSTALL_DIR/MonitorUpdater" > /dev/null 2>&1
  fi
  if ! $NEED_INSTALL_SERVER ; then
    sudo rm -rf "$WYN_INSTALL_DIR/$MODULE_SERVER" > /dev/null 2>/dev/null
    sudo rm -rf "$WYN_INSTALL_DIR/Plugins" > /dev/null 2>/dev/null
    sudo rm -rf "$WYN_INSTALL_DIR/sampledata" > /dev/null 2>/dev/null
    sudo rm -rf "$WYN_INSTALL_DIR/monetdb" > /dev/null 2>/dev/null
    sudo rm -rf "$WYN_INSTALL_DIR/DataSourceService" > /dev/null 2>/dev/null
    sudo rm -rf "$WYN_INSTALL_DIR/MemoryDBService" > /dev/null 2>/dev/null
    sudo rm -rf "$WYN_INSTALL_DIR/jre" > /dev/null 2>/dev/null
    sudo rm -rf "$WYN_INSTALL_DIR/SchedulerService" > /dev/null 2>/dev/null
  fi
  if ! $NEED_INSTALL_REPORTING_WORKER ; then
    sudo rm -rf "$WYN_INSTALL_DIR/$MODULE_REPORTING_WORKER" > /dev/null 2>/dev/null
  fi
  if ! $NEED_INSTALL_COT_WORKER ; then
    sudo rm -rf "$WYN_INSTALL_DIR/$MODULE_COT_WORKER" > /dev/null 2>/dev/null
  fi
  if ! $NEED_INSTALL_DASHBOARD_WORKER ; then
    sudo rm -rf "$WYN_INSTALL_DIR/$MODULE_DASHBOARD_WORKER" > /dev/null 2>/dev/null
  fi
}
installPostgreSQL() {
  stopService $WYN_DATABASE_SERVICE

  sudo mkdir -p $POSTGRES_INSTALL_DIR
  if [ ! -d "$POSTGRES_INSTALL_DIR/pgsql" ]; then
    sudo tar xzf $POSTGRES_FILE -C $POSTGRES_INSTALL_DIR > /dev/null 2>/dev/null
  fi
  
  # Add postgres user
  if ! id -u "$POSTGRES_ADMIN_NAME" >/dev/null 2>&1; then
    if [ "apt" == "$PKG_MGMT_CMD" ]; then
      if $SILENT_INSTALLATION ; then
        sudo adduser --system --disabled-password $POSTGRES_ADMIN_NAME >> $LOG_FILE 2>&1
      else
        sudo adduser --system --disabled-password $POSTGRES_ADMIN_NAME
      fi
    else
      sudo adduser -h > /dev/null 2>&1
      if [ "0" == "$?" ]; then
        if $SILENT_INSTALLATION ; then
          sudo adduser --system $POSTGRES_ADMIN_NAME >> $LOG_FILE 2>&1
        else
          sudo adduser --system $POSTGRES_ADMIN_NAME
        fi
      else
        if $SILENT_INSTALLATION ; then
          sudo useradd -r $POSTGRES_ADMIN_NAME >> $LOG_FILE 2>&1
        else
          sudo useradd -r $POSTGRES_ADMIN_NAME
        fi
      fi
    fi
  fi
  
  # Initialize database
  if [ ! -d "$POSTGRES_WYN_DATA_DIR" ]; then
    sudo mkdir -p $POSTGRES_WYN_DATA_DIR
    sudo chown -R $POSTGRES_ADMIN_NAME $POSTGRES_WYN_DATA_DIR
    
    cd $POSTGRES_INSTALL_DIR
    echo "$POSTGRES_ADMIN_PASSWORD" > $POSTGRES_PWFILE
    
    if [ "apt" == "$PKG_MGMT_CMD" ]; then
      if $SILENT_INSTALLATION ; then
        sudo -u $POSTGRES_ADMIN_NAME $POSTGRES_INITDB -D $POSTGRES_WYN_DATA_DIR -A $POSTGRES_AUTHMODE -E utf8 --pwfile=$POSTGRES_PWFILE >> $LOG_FILE 2>&1
        sudo -u $POSTGRES_ADMIN_NAME $POSTGRES_PGCTL -o "-p $POSTGRES_LISTENING_PORT" -D $POSTGRES_WYN_DATA_DIR -l $POSTGRES_WYN_DATA_DIR/logfile start >> $LOG_FILE 2>&1
        sudo -u $POSTGRES_ADMIN_NAME $POSTGRES_PGCTL -o "-p $POSTGRES_LISTENING_PORT" -D $POSTGRES_WYN_DATA_DIR stop >> $LOG_FILE 2>&1
      else
        sudo -u $POSTGRES_ADMIN_NAME $POSTGRES_INITDB -D $POSTGRES_WYN_DATA_DIR -A $POSTGRES_AUTHMODE -E utf8 --pwfile=$POSTGRES_PWFILE
        sudo -u $POSTGRES_ADMIN_NAME $POSTGRES_PGCTL -o "-p $POSTGRES_LISTENING_PORT" -D $POSTGRES_WYN_DATA_DIR -l $POSTGRES_WYN_DATA_DIR/logfile start
        sudo -u $POSTGRES_ADMIN_NAME $POSTGRES_PGCTL -o "-p $POSTGRES_LISTENING_PORT" -D $POSTGRES_WYN_DATA_DIR stop
      fi
    else
      sudo adduser -h > /dev/null 2>&1
      if [ "0" == "$?" ]; then
        if $SILENT_INSTALLATION ; then
          sudo -u $POSTGRES_ADMIN_NAME $POSTGRES_INITDB -D $POSTGRES_WYN_DATA_DIR -A $POSTGRES_AUTHMODE -E utf8 --pwfile=$POSTGRES_PWFILE >> $LOG_FILE 2>&1
          sudo -u $POSTGRES_ADMIN_NAME $POSTGRES_PGCTL -o "-p $POSTGRES_LISTENING_PORT" -D $POSTGRES_WYN_DATA_DIR -l $POSTGRES_WYN_DATA_DIR/logfile start >> $LOG_FILE 2>&1
          sudo -u $POSTGRES_ADMIN_NAME $POSTGRES_PGCTL -o "-p $POSTGRES_LISTENING_PORT" -D $POSTGRES_WYN_DATA_DIR stop >> $LOG_FILE 2>&1
        else
          sudo -u $POSTGRES_ADMIN_NAME $POSTGRES_INITDB -D $POSTGRES_WYN_DATA_DIR -A $POSTGRES_AUTHMODE -E utf8 --pwfile=$POSTGRES_PWFILE
          sudo -u $POSTGRES_ADMIN_NAME $POSTGRES_PGCTL -o "-p $POSTGRES_LISTENING_PORT" -D $POSTGRES_WYN_DATA_DIR -l $POSTGRES_WYN_DATA_DIR/logfile start
          sudo -u $POSTGRES_ADMIN_NAME $POSTGRES_PGCTL -o "-p $POSTGRES_LISTENING_PORT" -D $POSTGRES_WYN_DATA_DIR stop
        fi
      else
        if $SILENT_INSTALLATION ; then
          sudo runuser -m $POSTGRES_ADMIN_NAME -c '/opt/postgresql/pgsql/bin/initdb -D /opt/postgresql/data -A md5 -E utf8 --pwfile=/opt/postgresql/.pwf' >> $LOG_FILE 2>&1
          sudo runuser -m $POSTGRES_ADMIN_NAME -c '/opt/postgresql/pgsql/bin/pg_ctl -o "-p 5444" -D /opt/postgresql/data -l /opt/postgresql/data/logfile start' >> $LOG_FILE 2>&1
          sudo runuser -m $POSTGRES_ADMIN_NAME -c '/opt/postgresql/pgsql/bin/pg_ctl -o "-p 5444" -D /opt/postgresql/data stop' >> $LOG_FILE 2>&1
        else
          sudo runuser -m $POSTGRES_ADMIN_NAME -c '/opt/postgresql/pgsql/bin/initdb -D /opt/postgresql/data -A md5 -E utf8 --pwfile=/opt/postgresql/.pwf'
          sudo runuser -m $POSTGRES_ADMIN_NAME -c '/opt/postgresql/pgsql/bin/pg_ctl -o "-p 5444" -D /opt/postgresql/data -l /opt/postgresql/data/logfile start'
          sudo runuser -m $POSTGRES_ADMIN_NAME -c '/opt/postgresql/pgsql/bin/pg_ctl -o "-p 5444" -D /opt/postgresql/data stop'
        fi
      fi
    fi
    
    sudo rm $POSTGRES_PWFILE
    cd - > /dev/null 2>/dev/null
  fi
}
# install the default PostgreSQL dataase server
installDefaultDbProvider() {
  DB_PROVIDER="$DB_PROVIDER_POSTGRES"
  DATABASE_CONNECTIONSTRING="Host=localhost;Port=$POSTGRES_LISTENING_PORT;UserName=$POSTGRES_ADMIN_NAME;Password=$POSTGRES_ADMIN_PASSWORD"
  # Install PostgreSQL
  prtMsg "Install PostgreSQL ..."
  installPostgreSQL
  createService $WYN_DATABASE_SERVICE $WYN_DATABASE_SERVICE_SEQUENCE
  prtMsg "Install PostgreSQL finished"
}

installJRE() {
  if [ ! -d "$WYN_INSTALL_DIR/jre" ]; then
    sudo tar zxf "$WYN_INSTALL_DIR/$JRE8_FILE_NAME" -C "$WYN_INSTALL_DIR/"
  fi
}

# input: $1 - database provider, $2 - database server connection string
writeConfigFile() {
  local upperDbProvider="$(toUpper "$1")"
  local dbConnectionStringIs=$2
  local dbConnectionStringServer=$2
  local dbConnectionStringDatacache=$2
  
  local dbNameIs="wynis"
  local dbNameServer="wynserverdata"
  local dbNameDatacache="wyndatacache"
  if $SINGLE_DATABASE_MODE ; then
    dbNameIs="wyn"
    dbNameServer="wyn"
    dbNameDatacache="wyn"
  fi
  
  local semicolon=";"
  if [ ";" == "${2: -1}" ]; then
    semicolon=""
  fi
  
  if [ "$(toUpper "$DB_PROVIDER_POSTGRES")" == "$upperDbProvider" -o "$(toUpper "$DB_PROVIDER_MYSQL")" == "$upperDbProvider" ]; then
    dbConnectionStringIs="${2}${semicolon}Database=$dbNameIs;"
    dbConnectionStringServer="${2}${semicolon}Database=$dbNameServer;"
    dbConnectionStringDatacache="${2}${semicolon}Database=$dbNameDatacache;"
  elif [ "$(toUpper "$DB_PROVIDER_SQLSERVER")" == "$upperDbProvider" ]; then
    dbConnectionStringIs="${2}${semicolon}Initial Catalog=$dbNameIs;"
    dbConnectionStringServer="${2}${semicolon}Initial Catalog=$dbNameServer;"
    dbConnectionStringDatacache="${2}${semicolon}Initial Catalog=$dbNameDatacache;"
  fi
  
  if $ENCRYPT_DATABASE_CONNECTION_STRING ; then
    dbConnectionStringIs=$(encryptConnectionString "$dbConnectionStringIs")
    dbConnectionStringServer=$(encryptConnectionString "$dbConnectionStringServer")
    dbConnectionStringDatacache=$(encryptConnectionString "$dbConnectionStringDatacache")
  else
    dbConnectionStringIs=$(xmlEncode "$dbConnectionStringIs")
    dbConnectionStringServer=$(xmlEncode "$dbConnectionStringServer")
    dbConnectionStringDatacache=$(xmlEncode "$dbConnectionStringDatacache")
  fi
  
  local serverAddress="$HOST_IP_ADDRESS"
  if ! $NEED_INSTALL_SERVER ; then
    serverAddress="$BACKEND_SERVER_ADDRESS"
  fi
  
  local workerHost="localhost"
  if ! $NEED_INSTALL_SERVER ; then
    workerHost="$HOST_IP_ADDRESS"
  fi

  local lngCode=$(getLanguageCode)
  
  echo "<?xml version=\"1.0\" encoding=\"utf-8\"?>
<SystemConfig xmlns:sys=\"https://extendedxmlserializer.github.io/system\" xmlns=\"clr-namespace:ConfigMigration.Configuration.V90;assembly=ConfigMigration\">
  <Version>9.0</Version>
  <GlobalSettings>
    <IdentityServerUrl>http://$serverAddress:$WYN_SERVER_PORT</IdentityServerUrl>" > "$CONFIG_FILE_PATH"
  if $ENCRYPT_DATABASE_CONNECTION_STRING ; then
    echo "    <EnableEncryptedConnectionString>true</EnableEncryptedConnectionString>" >> "$CONFIG_FILE_PATH"
  fi
  echo "  </GlobalSettings>
  <Services>" >> "$CONFIG_FILE_PATH"
  if $NEED_INSTALL_SERVER ; then
    echo "    <Server>
      <Urls>http://*:$WYN_SERVER_PORT</Urls>
      <Storage>
        <StorageType>$DB_PROVIDER</StorageType>
        <ConnectionString>$dbConnectionStringServer</ConnectionString>
      </Storage>
      <DataExtraction>
        <StorageType>$DB_PROVIDER</StorageType>
        <ConnectionString>$dbConnectionStringDatacache</ConnectionString>
      </DataExtraction>
      <IdentityServer>
        <StorageType>$DB_PROVIDER</StorageType>
        <ConnectionString>$dbConnectionStringIs</ConnectionString>
      </IdentityServer>
      <Culture>
        <Culture>$lngCode</Culture>
      </Culture>
    </Server>" >> "$CONFIG_FILE_PATH"
  fi
  if $NEED_INSTALL_REPORTING_WORKER ; then
    echo "    <ReportingWorker />" >> "$CONFIG_FILE_PATH"
  fi
  if $NEED_INSTALL_COT_WORKER ; then
    echo "    <CotWorker />" >> "$CONFIG_FILE_PATH"
  fi
  if $NEED_INSTALL_DASHBOARD_WORKER ; then
    echo "    <DashboardWorker />" >> "$CONFIG_FILE_PATH"
  fi
  if $NEED_INSTALL_SERVER ; then
    echo "    <DataSourceService />
    <MemoryDBService />
    <SchedulerService />
    <AnalysisDBService />" >> "$CONFIG_FILE_PATH"
  fi
  echo "  </Services>" >> "$CONFIG_FILE_PATH"
  if ! $NEED_INSTALL_SERVER ; then
    if ! $SILENT_INSTALLATION ; then
      while :
      do
        read -p "Please enter the secret for the current secondary node(need to copy this secret from nodes management page): " SECONDARY_NODE_SECRET
        if [ "" != "$SECONDARY_NODE_SECRET" ]; then
          break
        fi
      done
    fi
    echo "  <Cluster>
    <Host>$HOST_IP_ADDRESS</Host>
    <Port>$WYN_MONITOR_PORT</Port>
    <Secret>$SECONDARY_NODE_SECRET</Secret>
    <Role>secondary</Role>
    <PrimaryNode>
      <Host>$BACKEND_SERVER_ADDRESS</Host>
      <Port>$WYN_MONITOR_PORT</Port>
    </PrimaryNode>
  </Cluster>" >> "$CONFIG_FILE_PATH"
  fi
  echo "</SystemConfig>
" >> "$CONFIG_FILE_PATH"
}
writeNonClusterConfigFile() {
  local upperDbProvider="$(toUpper "$1")"
  local dbConnectionStringIs=$2
  local dbConnectionStringServer=$2
  local dbConnectionStringDatacache=$2

  local dbNameIs="wynis"
  local dbNameServer="wynserverdata"
  local dbNameDatacache="wyndatacache"
  if $SINGLE_DATABASE_MODE ; then
    dbNameIs="wyn"
    dbNameServer="wyn"
    dbNameDatacache="wyn"
  fi

  local semicolon=";"
  if [ ";" == "${2: -1}" ]; then
    semicolon=""
  fi

  if [ "$(toUpper "$DB_PROVIDER_SQLITE")" == "$upperDbProvider" ]; then
    dbConnectionStringIs="Data source=../wyndbs/is.db;"
    dbConnectionStringServer="Data source=../wyndbs/storage.db;"
    dbConnectionStringDatacache="Data source=../wyndbs/extraction.db;"
  elif [ "$(toUpper "$DB_PROVIDER_POSTGRES")" == "$upperDbProvider" -o "$(toUpper "$DB_PROVIDER_MYSQL")" == "$upperDbProvider" ]; then
    dbConnectionStringIs="${2}${semicolon}Database=$dbNameIs;"
    dbConnectionStringServer="${2}${semicolon}Database=$dbNameServer;"
    dbConnectionStringDatacache="${2}${semicolon}Database=$dbNameDatacache;"
  elif [ "$(toUpper "$DB_PROVIDER_SQLSERVER")" == "$upperDbProvider" ]; then
    dbConnectionStringIs="${2}${semicolon}Initial Catalog=$dbNameIs;"
    dbConnectionStringServer="${2}${semicolon}Initial Catalog=$dbNameServer;"
    dbConnectionStringDatacache="${2}${semicolon}Initial Catalog=$dbNameDatacache;"
  fi

  if $ENCRYPT_DATABASE_CONNECTION_STRING ; then
    dbConnectionStringIs=$(encryptConnectionString "$dbConnectionStringIs")
    dbConnectionStringServer=$(encryptConnectionString "$dbConnectionStringServer")
    dbConnectionStringDatacache=$(encryptConnectionString "$dbConnectionStringDatacache")
  else
    dbConnectionStringIs=$(xmlEncode "$dbConnectionStringIs")
    dbConnectionStringServer=$(xmlEncode "$dbConnectionStringServer")
    dbConnectionStringDatacache=$(xmlEncode "$dbConnectionStringDatacache")
  fi

  duckDbConnectionString="Data source=../wyndbs/analysis.db"
  if $ENCRYPT_DATABASE_CONNECTION_STRING ; then
    duckDbConnectionString=$(encryptConnectionString "$duckDbConnectionString")
  fi

  local lngCode=$(getLanguageCode)

  sudo mkdir -p "$WYN_INSTALL_DIR/conf"
  sudo mkdir -p "$WYN_INSTALL_DIR/wyndbs"

  echo "<?xml version=\"1.0\" encoding=\"utf-8\"?>
<SystemConfig xmlns:sys=\"https://extendedxmlserializer.github.io/system\" xmlns=\"clr-namespace:ConfigMigration.Configuration.V90;assembly=ConfigMigration\">
  <Version>9.0</Version>
  <GlobalSettings>
    <IdentityServerUrl>http://$HOST_IP_ADDRESS:$WYN_SERVER_PORT</IdentityServerUrl>" > "$CONFIG_FILE_PATH"
  if $ENCRYPT_DATABASE_CONNECTION_STRING ; then
    echo "    <EnableEncryptedConnectionString>true</EnableEncryptedConnectionString>" >> "$CONFIG_FILE_PATH"
  fi
  echo "    <DataWarehouse>
      <Provider>DuckDB</Provider>
      <ConnectionString>$duckDbConnectionString</ConnectionString>
    </DataWarehouse>
  </GlobalSettings>
  <Services>
    <Server>
      <Urls>http://*:$WYN_SERVER_PORT</Urls>
      <Storage>
        <StorageType>$1</StorageType>
        <ConnectionString>$dbConnectionStringServer</ConnectionString>
      </Storage>
      <DataExtraction>
        <StorageType>$1</StorageType>
        <ConnectionString>$dbConnectionStringDatacache</ConnectionString>
      </DataExtraction>
      <IdentityServer>
        <StorageType>$1</StorageType>
        <ConnectionString>$dbConnectionStringIs</ConnectionString>
      </IdentityServer>
      <Culture>
        <Culture>$lngCode</Culture>
      </Culture>
      <SchedulerConfig>
        <Mode>Embedded</Mode>
      </SchedulerConfig>
      <ProductArchitecture>FullEmbedded</ProductArchitecture>
      <DisableJavaDataSource>true</DisableJavaDataSource>
    </Server>
    <ReportingWorker />
    <CotWorker />
    <DashboardWorker />
  </Services>
</SystemConfig>" >> "$CONFIG_FILE_PATH"
}

updateConfigFile() {
  prtMsg "Updating configuration..."
  if ! $CONFIG_FILE_EXISTS ; then
    SERVER_URI="http://$HOST_IP_ADDRESS:$WYN_SERVER_PORT"
  fi
  if $CLUSTER_MODE ; then
    writeConfigFile $DB_PROVIDER "$DATABASE_CONNECTIONSTRING"
  else
    writeNonClusterConfigFile $DB_PROVIDER "$DATABASE_CONNECTIONSTRING"
  fi
  prtMsg ""
}

getDatabaseSettings() {
  local defaultProvider=""
  local defaultHost="localhost"
  local defaultPort=""
  local defaultUser=""
  local defaultPswd=""
  local provider=""
  while :
  do
    if $CLUSTER_MODE ; then
      if [ "" == "$defaultProvider" ]; then
        defaultProvider="1"
        DB_PROVIDER="$DB_PROVIDER_POSTGRES"
        defaultPort=5432
        defaultUser="postgres"
      fi
      read -p "Please select the database provider(1 - $DB_PROVIDER_POSTGRES, 2 - $DB_PROVIDER_SQLSERVER, 3 - $DB_PROVIDER_MYSQL, 4 - $DB_PROVIDER_ORACLE): [$defaultProvider] " provider
    else
      if [ "" == "$defaultProvider" ]; then
        defaultProvider="5"
        DB_PROVIDER="$DB_PROVIDER_SQLITE"
      fi
      read -p "Please select the database provider(1 - $DB_PROVIDER_POSTGRES, 2 - $DB_PROVIDER_SQLSERVER, 3 - $DB_PROVIDER_MYSQL, 4 - $DB_PROVIDER_ORACLE, 5 - $DB_PROVIDER_SQLITE): [$defaultProvider] " provider
    fi

    if [ "" == "$provider" -o "$defaultProvider" == "$provider" ]; then
      break
    elif [ "1" == "$provider" ]; then
      defaultProvider="1"
      DB_PROVIDER="$DB_PROVIDER_POSTGRES"
      defaultPort=5432
      defaultUser="postgres"
      break
    elif [ "2" == "$provider" ]; then
      defaultProvider="2"
      DB_PROVIDER="$DB_PROVIDER_SQLSERVER"
      defaultPort=1433
      defaultUser="sa"
      break
    elif [ "3" == "$provider" ]; then
      defaultProvider="3"
      DB_PROVIDER="$DB_PROVIDER_MYSQL"
      defaultPort=3306
      defaultUser="root"
      break
    elif [ "4" == "$provider" ]; then
      defaultProvider="4"
      DB_PROVIDER="$DB_PROVIDER_ORACLE"
      defaultPort=1521
      defaultUser="sys"
      break
    elif ! $CLUSTER_MODE && [ "5" == "$provider" ]; then
      defaultProvider="5"
      DB_PROVIDER="$DB_PROVIDER_SQLITE"
    else
      continue
    fi
  done

  if [ "$DB_PROVIDER_SQLITE" != "$DB_PROVIDER" ]; then
    USE_EXISTING_DATABASE=true
    read -rp "Server host: [$defaultHost] " DB_HOST
    if [ "" == "$DB_HOST" ]; then
      DB_HOST="$defaultHost"
    else
      defaultHost="$DB_HOST"
    fi
    read -rp "Listening port: [$defaultPort] " DB_PORT
    if [ "" == "$DB_PORT" ]; then
      DB_PORT="$defaultPort"
    else
      defaultPort="$DB_PORT"
    fi
    IFS= read -rp "Username: [$defaultUser] " DB_USERNAME
    if [ "" == "$DB_USERNAME" ]; then
      DB_USERNAME="$defaultUser"
    else
      defaultUser="$DB_USERNAME"
    fi
    IFS= read -rsp "Password: " DB_PASSWORD
    while [ "" == "$DB_PASSWORD" ]
    do
      prtMsg ""
      IFS= read -rsp "Password can not be empty. Enter again: " DB_PASSWORD
    done
    prtMsg ""
    if [ "$DB_PROVIDER_ORACLE" == "$DB_PROVIDER" ]; then
      IFS= read -rp "Service Name: " ORACLE_SERVICE_NAME
      while [ "" == "$ORACLE_SERVICE_NAME" ]
      do
        IFS= read -rp "Service Name: " ORACLE_SERVICE_NAME
      done
    fi
  fi
}

# input: $1 - db provider, $2 - db host, $3 - db port, $4 - db user, $5 - db password, $6 - oracle service name
# output: database server connection string
# return: database server connection status
checkDatabaseServerConnection() {
  cd $WYN_INSTALL_DIR/Tools/ValidateDatabaseConnect
  local dbProviderBase64=$(base64Encode "$1")
  local dbHostBase64=$(base64Encode "$2")
  local dbPortBase64=$(base64Encode "$3")
  local dbUserBase64=$(base64Encode "$4")
  local dbPswdBase64=$(base64Encode "$5")
  local dbConnectionString=""
  if [ "$(toUpper "$DB_PROVIDER_ORACLE")" == "$(toUpper "$1")" ]; then
    local oracleServiceNameBase64="$(base64Encode "$6")"
    dbConnectionString=$(dotnet ./ValidateDatabaseConnect.dll -d "$dbProviderBase64" -h "$dbHostBase64" -P "$dbPortBase64" -u "$dbUserBase64" -p "$dbPswdBase64" -s "$oracleServiceNameBase64" -b 2>&1)
  else
    dbConnectionString=$(dotnet ./ValidateDatabaseConnect.dll -d "$dbProviderBase64" -h "$dbHostBase64" -P "$dbPortBase64" -u "$dbUserBase64" -p "$dbPswdBase64" -b 2>&1)
  fi
  local connectionStatus=$?
  cd - > /dev/null 2>/dev/null
  echo $dbConnectionString
  return $connectionStatus
}
# input: $1 - database provider, $2 - database server connection string
# output: database server connection string
# return: database server connection status
checkDatabaseServerConnection2() {
  cd $WYN_INSTALL_DIR/Tools/ValidateDatabaseConnect
  local dbProviderBase64=$(base64Encode "$1")
  local dbConnectionStringBase64=$(base64Encode "$2")
  local dbConnectionString=""
  dbConnectionString=$(dotnet ./ValidateDatabaseConnect.dll -d "$dbProviderBase64" -c "$dbConnectionStringBase64" -b 2>&1)
  local dbConnectionStatus=$?
  cd - > /dev/null 2>/dev/null
  echo $dbConnectionString
  return $dbConnectionStatus
}

# input: $1 - database server connection status
# return: database server connection status
handleIncorrectDatabaseServerConnection() {
  local connectionStatus=$1
  local operation=""
  while :
  do
    if [ "1" == "$connectionStatus" ]; then
      prtMsg "Connect to database server failed, please reconfigure the database server information."
      break
    elif [ "2" == "$connectionStatus" ]; then
      prtMsg "The required minimum value of variable 'max_allowed_packet' is 300MB, please set it using command 'SET GLOBAL  max_allowed_packet=314572800;' and then try again."
      sleep 1
      while :
      do
        read -p "Would you like to try again(1) or go back to reconfigure the database server information(2)? [1]" operation
        if [ "" == "$operation" -o "1" == "$operation" ]; then
          operation="1"
          break
        elif [ "2" == "$operation" ]; then
          break
        else
          continue
        fi
      done
      if [ "1" == "$operation" ]; then
        # check database server connection
        prtMsg "Checking the database server connection ..."
        DATABASE_CONNECTIONSTRING=$(checkDatabaseServerConnection "$DB_PROVIDER" "$DB_HOST" "$DB_PORT" "$DB_USERNAME" "$DB_PASSWORD" "$ORACLE_SERVICE_NAME")
        connectionStatus=$?
        if [ "0" == "$connectionStatus" ]; then
          DATABASE_CONNECTIONSTRING="$(base64Decode "$DATABASE_CONNECTIONSTRING")"
        fi
      elif [ "2" == "$operation" ]; then
        break
      fi
    elif [ "3" == "$connectionStatus" ]; then
      prtMsg "The mininum version of the supported Postgres database is 10, please reconfigure the database server information."
      break
    else
      break
    fi
  done
  return $connectionStatus
}

#configDatabase() {
#  while :
#  do
#    local connectionStatus=""
#    local useExistingDb=""
#    while [ "y" != "$useExistingDb" -a "n" != "$useExistingDb" -a "Y" != "$useExistingDb" -a "N" != "$useExistingDb" ]
#    do
#      read -p "Would you like to use an existing database server? Selecting 'no' will install a PostgreSQL server on this machine. (y/n): " useExistingDb
#    done
#    # use an existing database server
#    if [ "y" == "$useExistingDb" -o "Y" == "$useExistingDb" ]; then
#      USE_EXISTING_DATABASE=true
#      # get database settings
#      getDatabaseSettings
#      # check database server connection
#      prtMsg "Checking the database server connection ..."
#      DATABASE_CONNECTIONSTRING=$(checkDatabaseServerConnection "$DB_PROVIDER" "$DB_HOST" "$DB_PORT" "$DB_USERNAME" "$DB_PASSWORD" "$ORACLE_SERVICE_NAME")
#      connectionStatus=$?
#      if [ "0" == "$connectionStatus" ]; then
#        DATABASE_CONNECTIONSTRING="$(base64Decode "$DATABASE_CONNECTIONSTRING")"
#      else
#        handleIncorrectDatabaseServerConnection $connectionStatus
#        connectionStatus=$?
#      fi
#    # use built-in database
#    elif [ "n" == "$useExistingDb" -o "N" == "$useExistingDb" ]; then
#      USE_EXISTING_DATABASE=false
#      connectionStatus="0"
#      installDefaultDbProvider
#    fi
#    if [ "0" == "$connectionStatus" ]; then
#      break
#    fi
#  done
#}
configDatabase() {
  while :
  do
    # get database settings
    getDatabaseSettings
    if [ "$(toUpper "$DB_PROVIDER_SQLITE")" == "$(toUpper "$DB_PROVIDER")" ]; then
      break
    fi
    # check database server connection
    prtMsg "Checking the database server connection..."
    local connectionStatus=""
    DATABASE_CONNECTIONSTRING=$(checkDatabaseServerConnection "$DB_PROVIDER" "$DB_HOST" "$DB_PORT" "$DB_USERNAME" "$DB_PASSWORD" "$ORACLE_SERVICE_NAME")
    connectionStatus=$?
    if [ "0" == "$connectionStatus" ]; then
      DATABASE_CONNECTIONSTRING="$(base64Decode "$DATABASE_CONNECTIONSTRING")"
    else
      handleIncorrectDatabaseServerConnection $connectionStatus
      connectionStatus=$?
    fi
    if [ "0" == "$connectionStatus" ]; then
      break
    fi
  done
}
# check if need encrypt database connection string
# return - 0: need encrypt
needEncryptConnectionString() {
  local ret=1
  while :
  do
    read -p "Would you like to encrypt the database connection string in the configuration file(y/n)? [n]" encryptCS
    if [ "y" == "$encryptCS" -o "Y" == "$encryptCS" ]; then
      ENCRYPT_DATABASE_CONNECTION_STRING=true
      ret=0
      break
    elif [ "" == "$encryptCS" -o "n" == "$encryptCS" -o "N" == "$encryptCS" ]; then
      ENCRYPT_DATABASE_CONNECTION_STRING=false
      ret=1
      break
    else
      continue
    fi
  done
  return $ret
}
# encrypt database connection string
# input: $1 - database connection string
# output: encrypted database connection string
# return: 0 - success, 1 - fail
encryptConnectionString() {
  cd "$WYN_INSTALL_DIR/Tools/EncryptOrDecryptString"
  encryptedConnectionString=$(dotnet ./eods.dll 0 "$1")
  status=$?
  if [ "0" == "$status" ]; then
    encryptedConnectionString=$(substring "$encryptedConnectionString" 19)
  fi
  echo $encryptedConnectionString
  cd - > /dev/null 2>/dev/null
  return $status
}
# input: $1 - server uri
waitForWynStartup() {
  times=0
  if [ "" != "$1" ]; then
    while [ $times -lt 150 ]
    do
      if [ "200" == "$(curl -o /dev/null -m 2 -sw '%{http_code}' $1/api/healthcheck)" ]; then
        break
      else
        times=$(($times+1))
        sleep 2
      fi
    done
  else
    sleep 30
  fi
  
  local ret=0
  if [ "" != "$1" ]; then
    if [ $times -lt 150 ]; then
      prtMsg "Now you can visit Wyn on $1 using the account 'admin', enjoy it."
    else
      prtMsg "Start service failed, please check your configuration, and restart the wyn service manually."
      ret=1
    fi
  else
    prtMsg "Finished."
  fi

  return $ret
}

startAllServices() {
  if [ "$SERVICE_MANAGE_COMMAND" == "$SYSTEMCTL" ]; then
    sudo systemctl daemon-reload > /dev/null 2>/dev/null
  fi
  
  #if ! $CONFIG_FILE_EXISTS && $NEED_INSTALL_SERVER && ! $USE_EXISTING_DATABASE ; then
  #  startService $WYN_DATABASE_SERVICE
  #  sleep 3
  #fi
  
  startService $WYN_SERVICE
}

installSampleData() {
  if $NEED_INSTALL_SERVER ; then
    if ! $SILENT_INSTALLATION && ! $CONFIG_FILE_EXISTS ; then
      while :
      do
        read -p "Would you like to import the sample documents? Importing the sample documents helps beginners learn how to use Wyn. (y/n): [y]" importSamples
        if [ "" == "$importSamples" -o "Y" == "$importSamples" -o "y" == "$importSamples" ]; then
          IMPORT_SAMPLES=true
          break
        elif [ "N" == "$importSamples" -o "n" == "$importSamples" ]; then
          IMPORT_SAMPLES=false
          break
        else
          continue
        fi
      done
    fi

    if ! $IMPORT_SAMPLES ; then
      prtMsg "Removing the built-in sample files..."
      sudo rm -rf "$WYN_INSTALL_DIR/sampledata/" > /dev/null 2>/dev/null
      sudo rm -rf "$WYN_INSTALL_DIR/$MODULE_SERVER/sample_files/" > /dev/null 2>/dev/null
    elif [ "" != "$SAMPLE_FILE" ]; then
      prtMsg "Removing the built-in sample files..."
      sudo rm -rf "$WYN_INSTALL_DIR/sampledata/" > /dev/null 2>/dev/null
      sudo rm -rf "$WYN_INSTALL_DIR/$MODULE_SERVER/sample_files/"* > /dev/null 2>/dev/null
      sudo rm -rf "$WYN_INSTALL_DIR/$MODULE_SERVER/sample_files/.finished" > /dev/null 2>/dev/null
      if [ -f "$SAMPLE_FILE" ]; then
        prtMsg "Copying custom sample file '$SAMPLE_FILE'..."
        sudo cp "$SAMPLE_FILE" "$WYN_INSTALL_DIR/$MODULE_SERVER/sample_files/sample_files.zip" > /dev/null 2>/dev/null
      else
        prtMsg "The sample data '{$SAMPLE_FILE}' does not exist, so no sample data will be imported."
      fi
    fi
  fi

  prtMsg ""
}
# get back-end server address
checkBackendServerAddr() {
  if ! $NEED_INSTALL_SERVER && [ "" == "$BACKEND_SERVER_ADDRESS" ] ; then
    if $SILENT_INSTALLATION ; then
      prtMsg "The address of back-end server MUST be specified if you don't want to install the module '$MODULE_SERVER' on this server."
      showUsage
      exit $EXIT_CODE_NO_BACKEND_SERVER_ADDRESS
    else
      while :
      do
        read -p "Please enter the back-end server address: " serverAddress
        if [ "" != "$serverAddress" ]; then
          BACKEND_SERVER_ADDRESS="$serverAddress"
          break
        else
          continue
        fi
      done
    fi
  fi
}
# remove useless files
cleanup() {
  if [ -f "$POSTGRES_FILE" ]; then
    sudo rm "$POSTGRES_FILE"
  fi
  if [ -f "$POSTGRES_PWFILE" ]; then
    sudo rm "$POSTGRES_PWFILE"
  fi
  if [ -d "$SERVICES_FOLDER" ]; then
    sudo rm -rf "$SERVICES_FOLDER"
  fi
  if [ -f "$FONT_FILE" ]; then
    sudo rm "$FONT_FILE"
  fi
  if [ -f "$MONETDB_FILE" ]; then
    sudo rm -rf "$MONETDB_FILE"
  fi
  if [ -d "/tmp/monetdb" ]; then
    sudo rm -rf "/tmp/monetdb"
  fi
  if [ -f "$WYN_INSTALL_DIR/$JRE8_FILE_NAME" ]; then
    sudo rm "$WYN_INSTALL_DIR/$JRE8_FILE_NAME"
  fi
  if [ -d "$WYN_INSTALL_DIR/monetdb" ]; then
    sudo rm -rf "$WYN_INSTALL_DIR/monetdb"
  fi
}
# register trial user information
registerTrialInfo() {
  if $NEED_INSTALL_SERVER && [ -d "$WYN_INSTALL_DIR/Tools/RegisterTrialInfo" ] && [ ! -f "$WYN_INSTALL_DIR/Tools/RegisterTrialInfo/.registered" ] ; then
    cd "$WYN_INSTALL_DIR/Tools/RegisterTrialInfo"
    dotnet ./RegisterTrialInfo.dll "https://wyn-licensing.mescius.io/RegisterTrialInfo"
    sudo echo "$?" > "$WYN_INSTALL_DIR/Tools/RegisterTrialInfo/.registered"
    prtMsg ""
    cd - > /dev/null 2>/dev/null
  fi
}

# input: $1 - database provider
# return: 0 - valid, 1 - invalid
isValidDatabaseProvider() {
  local provider=$(toUpper "$1")
  if [ "$provider" == "$(toUpper "$DB_PROVIDER_POSTGRES")" ]; then
    return 0
  elif [ "$provider" == "$(toUpper "$DB_PROVIDER_SQLSERVER")" ]; then
    return 0
  elif [ "$provider" == "$(toUpper "$DB_PROVIDER_MYSQL")" ]; then
    return 0
  elif [ "$provider" == "$(toUpper "$DB_PROVIDER_ORACLE")" ]; then
    return 0
  elif [ "$provider" == "$(toUpper "$DB_PROVIDER_SQLITE")" ]; then
    if $CLUSTER_MODE ; then
      return 1
    else
      return 0
    fi
  else
    return 1
  fi
}

# input: $1 - license register API address, $2 - license key
# return: 0 - success, 1 - failed
registerLicense() {
  prtMsg "Registering license key..."
  local success='"success":true'
  local response=""
  response=$(curl -s -X POST -H "Content-Type: application/json" --data "$2" "$1")
  strContains "$response" "$success" 0
  local ret=$?
  if [ "0" == "$ret" ]; then
    prtMsg "License key was successfully registered."
  else
    prtMsg "Register license key failed, please refer to the response: $response"
  fi
  return $ret
}

# store all the data in a single database?
checkSingleDatabaseMode() {
  while :
  do
    read -p "Would you like to store all the data in a single database(y/n)? [n]" singleDb
    if [ "" == "$singleDb" -o "n" == "$singleDb" -o "N" == "$singleDb" ]; then
      SINGLE_DATABASE_MODE=false
      break
    elif [ "y" == "$singleDb" -o "Y" == "$singleDb" ]; then
      SINGLE_DATABASE_MODE=true
      break
    else
      continue
    fi
  done
}
# set password for admin user
inputAdminPassword() {
  while :
  do
    read -rsp "Please enter a new password for administrator user 'admin': " password
    strLength "$password"
    if [ 0 -eq $? -o 32 -lt $? ]; then
      echo ""
      echo "The password cannot be empty and the length of the password should be less than 32, please enter again."
      continue
    else
      isEmptyOrWhitespace "$password"
      if [ 0 -eq $? ]; then
        echo ""
        echo "The password cannot only contains whitespace, please enter again."
        continue
      else
        echo ""
        read -rsp "Please confirm your password: " confirmPassword
        if [ "$confirmPassword" != "$password" ]; then
          echo ""
          echo "The password and the confirm password are not match, please enter again."
          continue
        else
          ADMIN_PASSWORD="$password"
          echo ""
          break
        fi
      fi
   fi
  done
  prtMsg ""
}
generateCredentialFile() {
  CREDENTIAL=$(base64Encode "$ADMIN_PASSWORD")
  sudo echo "$CREDENTIAL" > "$CREDENTIAL_FILE"
}
# input - $1 - port
addPortInIptables() {
  sudo iptables -C INPUT -j ACCEPT -p tcp --dport $1 > /dev/null 2>&1
  if [ "0" != "$?" ]; then
    sudo iptables -A INPUT -p tcp --dport $1 -j ACCEPT > /dev/null 2>&1
    sudo iptables-save > /dev/null 2>&1
  fi
}
# input: $1 - port
addPortInUfw() {
  sudo ufw allow $1 > /dev/null 2>&1
}
addPortInFirewallCmd() {
  sudo firewall-cmd --zone=public --add-port=$1/tcp --permanent > /dev/null 2>&1
  sudo firewall-cmd --reload > /dev/null 2>&1
}
# input: $1 - port
addPortInFirewall() {
  ufw --version > /dev/null 2>&1
  if [ "0" == "$?" ]; then
    addPortInUfw $1
  else
    firewall-cmd --version > /dev/null 2>&1
    if [ "0" == "$?" ]; then
      addPortInFirewallCmd $1
    else
      iptables --version > /dev/null 2>&1
      if [ "0" == "$?" ]; then
        addPortInIptables $1
      fi
    fi
  fi
}
# update firewall rules
updateFirewall() {
  if $NEED_INSTALL_SERVER ; then
    addPortInFirewall $WYN_MONITOR_PORT
    addPortInFirewall $WYN_SERVER_PORT
  else
    if $NEED_INSTALL_REPORTING_WORKER ; then
      addPortInFirewall $WYN_REPORTING_WORKER_PORT
    fi
    if $NEED_INSTALL_COT_WORKER ; then
      addPortInFirewall $WYN_COT_WORKER_PORT
    fi
    if $NEED_INSTALL_DASHBOARD_WORKER ; then
      addPortInFirewall $WYN_DASHBOARD_WORKER_PORT
    fi
  fi
}
# get UBUNTU codename
codename() {
  local text=$(cat /etc/os-release | grep UBUNTU_CODENAME)
  local codename="${text//UBUNTU_CODENAME=/}"
  echo "$codename"
}
# get EL version
elVersion() {
  local version=$(cat /etc/os-release | grep PLATFORM_ID | sed 's/[^0-9]//g')
  echo "$version"
}
# install MonetDB
installMonetDB() {
  local monetdbPort=54321
  
  if [ -d "/tmp/monetdb" ]; then
    sudo rm -rf "/tmp/monetdb"
  fi
  sudo tar zxf "$MONETDB_FILE" -C "/tmp/"
  if [ "zypper" == "$PKG_MGMT_CMD" ]; then
    cd "/tmp/monetdb/suse/15"
  elif [ "yum" == "$PKG_MGMT_CMD" ]; then
    local elVer="$(elVersion)"
    if [ "" == "$elVer" ]; then
      cd "/tmp/monetdb/epel/7"
    else
      if [ -d "/tmp/monetdb/epel/$elVer" ]; then
        cd "/tmp/monetdb/epel/$elVer"
      else
        prtMsg "Won't install MonetDB on this server since unsupported OS version detected, the version is 'epel:$elVer'."
        return 1
      fi
    fi
  else
    local codename="$(codename)"
    if [ -d "/tmp/monetdb/ubuntu/$codename" ]; then
      cd "/tmp/monetdb/ubuntu/$codename"
    else
      prtMsg "Won't install MonetDB on this server since unsupported Ubuntu version detected, the codename is '$codename'."
      return 1
    fi
  fi
  if $SILENT_INSTALLATION ; then
    sudo bash ./monetdb-install.sh >> $LOG_FILE 2>&1
    if [ "0" == "$?" ]; then
      prtMsg "MonetDB installed successfully."
    else
      prtMsg "Install MonetDB failed."
    fi
  else
    sudo bash ./monetdb-install.sh
    if [ "0" == "$?" ]; then
      prtMsg "MonetDB installed successfully."
    else
      prtMsg "Install MonetDB failed."
    fi
  fi
  cd - > /dev/null 2>/dev/null
  prtMsg ""
  return 0
}
# uninstall MonetDB
uninstallMonetDB() {
  if [ -d "/tmp/monetdb" ]; then
    sudo rm -rf "/tmp/monetdb"
  fi
  sudo tar zxf "$MONETDB_FILE" -C "/tmp/"
  if [ "zypper" == "$PKG_MGMT_CMD" ]; then
    cd "/tmp/monetdb/suse/15"
  elif [ "yum" == "$PKG_MGMT_CMD" ]; then
    local elVer="$(elVersion)"
    if [ "" == "$elVer" ]; then
      cd "/tmp/monetdb/epel/7"
    else
      if [ -d "/tmp/monetdb/epel/$elVer" ]; then
        cd "/tmp/monetdb/epel/$elVer"
      else
        return
      fi
    fi
  else
    local codename="$(codename)"
    if [ -d "/tmp/monetdb/ubuntu/$codename" ]; then
      cd "/tmp/monetdb/ubuntu/$codename"
    else
      return
    fi
  fi
  if $SILENT_INSTALLATION ; then
    sudo bash ./monetdb-uninstall.sh >> $LOG_FILE 2>&1
  else
    sudo bash ./monetdb-uninstall.sh
  fi
  cd - > /dev/null 2>/dev/null
}
# output: version text
getMonetDBVersion() {
  # MonetDB Database Server v11.37.11 (Jun2020-SP1)
  local text=$(monetdbd --version)
  local version=$(substring "$text" 26 33)
  version=$(removeWhitespace "$version")
  echo "$version"
}
# input: $1 - version1, $2 - version2
# return: 0 - equals, 1 - less than, 2 - great than
monetDBVersionCompare() {
  for idx in 1 2 3
  do
    local v1=$(echo "$1" | cut -d '.' -f $idx)
    local v2=$(echo "$2" | cut -d '.' -f $idx)
    if [ "$v1" -lt "$v2" ]; then
      return 1
    elif [ "$v1" -gt "$v2" ]; then
      return 2
    fi
  done
  return 0
}
# return: 0 - need install, 1 - does not need, 2 - need uninstall and then install
needInstallMonetDB() {
  if ! $CLUSTER_MODE ; then
    return 1
  fi
  if $CONFIG_FILE_EXISTS ; then
    local targetVersion="11.43.23"
    monetdbd --version > /dev/null 2>&1
    if [ "0" == "$?" ]; then
      local currentVersion=$(getMonetDBVersion)
      monetDBVersionCompare "$currentVersion" "$targetVersion"
      local compareRst=$?
      if [ "2" == "$compareRst" ]; then
        return 1
      elif [ "1" == "$compareRst" ]; then
        return 2
      else
        return 1
      fi
    fi
  else
    return 0
  fi
}
# select installation mode
selectInstallationMode() {
  while :
  do
    read -p "Would you like to install Wyn with the single-process mode(y/n)? [y]" singleProcess
    if [ "" == "$singleProcess" -o "y" == "$singleProcess" -o "Y" == "$singleProcess" ]; then
      CLUSTER_MODE=false
      DB_PROVIDER="$DB_PROVIDER_SQLITE"
      CONFIG_FILE_PATH="$WYN_INSTALL_DIR/conf/$CONFIG_FILE_NAME"
      break
    elif [ "n" == "$singleProcess" -o "N" == "$singleProcess" ]; then
      CLUSTER_MODE=true
      CONFIG_FILE_PATH="$WYN_INSTALL_DIR/$MODULE_MONITOR/conf/$CONFIG_FILE_NAME"
      break
    else
      continue
    fi
  done
  prtMsg ""
}
setAdminPassword() {
  # admin password
  if $NEED_INSTALL_SERVER ; then
    if ! $SILENT_INSTALLATION ; then
      inputAdminPassword
    else
      # check password
      strLength "$ADMIN_PASSWORD"
      if [ 0 -eq $? -o 32 -lt $? ]; then
        prtMsg "Password cannot be empty and the length of the password should be less than 32."
        exit $EXIT_CODE_INVALID_PASSWORD
      fi
      isEmptyOrWhitespace "$ADMIN_PASSWORD"
      if [ 0 -eq $? ]; then
        prtMsg "Password cannot only contains whitespace."
        exit $EXIT_CODE_INVALID_PASSWORD
      fi
    fi
    generateCredentialFile
  fi
}
inputDefaultLanguage() {
  while :
  do
    read -p "Please select the default language(1 - $LNG_ENGLISH, 2 - $LNG_KOREAN): [1] " language
    if [ "" == "$language" -o "1" == "$language" ]; then
      DEFAULT_LANGUAGE="$LNG_ENGLISH"
      break
    elif [ "2" == "$language" ]; then
      DEFAULT_LANGUAGE="$LNG_KOREAN"
      break
    else
      continue
    fi
  done
}
setDefaultLanguage() {
  if $NEED_INSTALL_SERVER ; then
    if ! $SILENT_INSTALLATION ; then
      inputDefaultLanguage
	  prtMsg ""
    else
      if [ "${LNG_ENGLISH^^}" == "${DEFAULT_LANGUAGE^^}" -o "${LNG_ENGLISH_CODE^^}" == "${DEFAULT_LANGUAGE^^}" ]; then
        DEFAULT_LANGUAGE="$LNG_ENGLISH"
	  elif [ "${LNG_KOREAN^^}" == "${DEFAULT_LANGUAGE^^}" -o "${LNG_KOREAN_CODE^^}" == "${DEFAULT_LANGUAGE^^}" ]; then
        DEFAULT_LANGUAGE="$LNG_KOREAN"
	  else
        prtMsg "Invalid default language '$DEFAULT_LANGUAGE' detected, the available values are '$LNG_ENGLISH' and '$LNG_KOREAN'. Will use '$LNG_ENGLISH' as the default language."
        DEFAULT_LANGUAGE="$LNG_ENGLISH"
      fi
    fi
  fi
}
getLanguageCode() {
  if [ "${LNG_KOREAN^^}" == "${DEFAULT_LANGUAGE^^}" -o "${LNG_KOREAN_CODE^^}" == "${DEFAULT_LANGUAGE^^}" ]; then
    echo "$LNG_KOREAN_CODE"
  else
    echo "$LNG_ENGLISH_CODE"
  fi
}

#====================================== INSTALLATION ======================================#

trap "trap_ctrlc" INT

crtPath=$(pwd)
LOG_FILE="$crtPath/$LOG_FILE"

# parse and check the parameters
parseArguments "$@"
exit_code=$?
if [ "0" != "$exit_code" ]; then
  showUsage
  exit $exit_code
fi

# check dotnet link
if [ ! -L /usr/bin/dotnet ] && [ -f /usr/share/dotnet/dotnet ] ; then
  sudo ln -s /usr/share/dotnet/dotnet /usr/bin/dotnet
fi
# replace openssl.cnf
if [ -f /etc/ssl/openssl.cnf ]; then
  sudo cp "$SERVICES_FOLDER/openssl.cnf" /etc/ssl/openssl.cnf
fi

if $SILENT_INSTALLATION ; then
  prtMsg "============================== INSTALLATION START =============================="
  # check database settings
  if [ "" == "$DB_PROVIDER" ]; then
    prtMsg "No database provider provided."
    exit $EXIT_CODE_INVALID_DATABASE_PROVIDER
  else
    isValidDatabaseProvider "$DB_PROVIDER"
    if [ "0" != "$?" ]; then
      prtMsg "Invalid database provider '$DB_PROVIDER' detected, the supported database providers are '$DB_PROVIDER_POSTGRES', '$DB_PROVIDER_SQLSERVER', '$DB_PROVIDER_MYSQL', '$DB_PROVIDER_ORACLE', and '$DB_PROVIDER_SQLITE'."
      prtMsg "And the database provider '$DB_PROVIDER_SQLITE' is only available when deploying Wyn using the single-database mode."
      exit $EXIT_CODE_INVALID_DATABASE_PROVIDER
    fi
  fi
  if [ "" == "$DATABASE_CONNECTIONSTRING" -a "$(toUpper "$DB_PROVIDER_SQLITE")" != "$(toUpper "$DB_PROVIDER")" ]; then
    prtMsg "No database connection string provided."
    exit $EXIT_CODE_INVALID_DATABASE_CONNECTION_STRING
  fi
fi

# get host IP address
HOST_IP_ADDRESS=$(getHostIp)

# check system information
checkSystemInfo

# check if configuration file exists, if yes, backup configuration file
if [ -f "$WYN_INSTALL_DIR/conf/$CONFIG_FILE_NAME" ]; then
  CLUSTER_MODE=false
  CONFIG_FILE_PATH="$WYN_INSTALL_DIR/conf/$CONFIG_FILE_NAME"
  CONFIG_FILE_EXISTS=true
elif [ -f "$WYN_INSTALL_DIR/$MODULE_MONITOR/conf/$CONFIG_FILE_NAME" ]; then
  CLUSTER_MODE=true
  CONFIG_FILE_PATH="$WYN_INSTALL_DIR/$MODULE_MONITOR/conf/$CONFIG_FILE_NAME"
  CONFIG_FILE_EXISTS=true
fi
if $CONFIG_FILE_EXISTS ; then
  sudo cp "$CONFIG_FILE_PATH" "$CONFIG_FILE_PATH.$(date +"%Y%m%d%H%M%S")"
fi

# get the modules to be installed
# the existing of file '.modules' means that the installation is upgrading from previous version(>= 4.0), we should install the modules which the previous version installed
if [ -f "$MODULES_FILE" ]; then
  modulesStr="$(<"$MODULES_FILE")"
  # incorrect modules file content, refer to GEF-10212
  if [ "" == "$modulesStr" ]; then
    unset MODULES
    MODULES[0]="$MODULE_SERVER"
    MODULES[1]="$MODULE_REPORTING_WORKER"
    MODULES[2]="$MODULE_COT_WORKER"
    MODULES[3]="$MODULE_DASHBOARD_WORKER"
  else
    parseModules "$modulesStr"
  fi
else
  # the existing of file 'Wyn.conf' means that the installation is upgrading from previous version(< 4.0), we should install all the modules since the previous version does not support partial installation
  if $CONFIG_FILE_EXISTS ; then
    unset MODULES
    MODULES[0]="$MODULE_SERVER"
    MODULES[1]="$MODULE_REPORTING_WORKER"
    MODULES[2]="$MODULE_COT_WORKER"
    MODULES[3]="$MODULE_DASHBOARD_WORKER"
  # fresh installation
  else
    # silent installation
    if $SILENT_INSTALLATION ; then
      # install all the modules if user does not specify any module
      if [ "0" == "${#MODULES[@]}" ]; then
        unset MODULES
        MODULES[0]="$MODULE_SERVER"
        MODULES[1]="$MODULE_REPORTING_WORKER"
        MODULES[2]="$MODULE_COT_WORKER"
        MODULES[3]="$MODULE_DASHBOARD_WORKER"
      # check if there are invalid modules that user specified
      else
        prtMsg "Checking need installed modules..."
        invalidModules="$(hasInvalidModules)"
        if [ "0" == "$?" ]; then
          prtMsg "Invalid modules '$invalidModules' detected."
          showUsage
          exit $EXIT_CODE_INVALID_MODULES_DETECTED
        fi
      fi
    # interactive installation
    else
      # select all the modules
      unset MODULES
      MODULES[0]="$MODULE_SERVER"
      MODULES[1]="$MODULE_REPORTING_WORKER"
      MODULES[2]="$MODULE_COT_WORKER"
      MODULES[3]="$MODULE_DASHBOARD_WORKER"
      echo "$MODULE_SERVER,$MODULE_REPORTING_WORKER,$MODULE_COT_WORKER,$MODULE_DASHBOARD_WORKER" > "$MODULES_FILE_NAME"
    fi
  fi
fi

needInstallModule $MODULE_SERVER
if [ "0" == "$?" ]; then
  NEED_INSTALL_SERVER=true
fi
needInstallModule $MODULE_REPORTING_WORKER
if [ "0" == "$?" ]; then
  NEED_INSTALL_REPORTING_WORKER=true
fi
needInstallModule $MODULE_COT_WORKER
if [ "0" == "$?" ]; then
  NEED_INSTALL_COT_WORKER=true
fi
needInstallModule $MODULE_DASHBOARD_WORKER
if [ "0" == "$?" ]; then
  NEED_INSTALL_DASHBOARD_WORKER=true
fi

# check modules
if ! $NEED_INSTALL_SERVER && ! $NEED_INSTALL_REPORTING_WORKER && ! $NEED_INSTALL_COT_WORKER && ! $NEED_INSTALL_DASHBOARD_WORKER ; then
  prtMsg "No modules were selected to install."
  exit $EXIT_CODE_NO_MODULES_SELECTED
fi

# check back-end server address
if ! $CONFIG_FILE_EXISTS && $CLUSTER_MODE ; then
  checkBackendServerAddr
fi

# register trial user information
if ! $SILENT_INSTALLATION && ! $CONFIG_FILE_EXISTS ; then
  registerTrialInfo
fi

# select Wyn edition
if ! $CONFIG_FILE_EXISTS && ! $SILENT_INSTALLATION ; then
  selectInstallationMode
fi

# configure database information
if ! $CONFIG_FILE_EXISTS ; then
  if $NEED_INSTALL_SERVER ; then
    if ! $SILENT_INSTALLATION ; then
      configDatabase
      # single database mode?
      if [ "$(toUpper "$DB_PROVIDER_ORACLE")" != "$(toUpper "$DB_PROVIDER")" -a "$(toUpper "$DB_PROVIDER_SQLITE")" != "$(toUpper "$DB_PROVIDER")" ]; then
        checkSingleDatabaseMode
      fi
      # need encrypt database connection string?
      if [ "$(toUpper "$DB_PROVIDER_SQLITE")" != "$(toUpper "$DB_PROVIDER")" ]; then
        needEncryptConnectionString
      else
        sudo mkdir -p "$WYN_INSTALL_DIR/wyndbs"
      fi
      prtMsg ""
    else
      # check database connection string
      if [ "$DB_PROVIDER" != "$DB_PROVIDER_SQLITE" ] && $USE_EXISTING_DATABASE ; then
        prtMsg "Checking database connection..."
        DATABASE_CONNECTIONSTRING=$(checkDatabaseServerConnection2 "$DB_PROVIDER" "$DATABASE_CONNECTIONSTRING")
        status=$?
        if [ "0" == "$status" ]; then
          DATABASE_CONNECTIONSTRING="$(base64Decode "$DATABASE_CONNECTIONSTRING")"
        elif [ "1" == "$status" ]; then
          prtMsg "Connect to database server failed, please check your database server connection string and try again."
          exit $EXIT_CODE_INVALID_DATABASE_CONNECTION_STRING
        elif [ "2" == "$status" ]; then
          prtMsg "The required minimum value of variable 'max_allowed_packet' is 300MB, please modify your MySQL configuration file to match the requirement and restart the MySQL service, and then try again."
          exit $EXIT_CODE_INVALID_MYSQL_REQUIREMENT
        elif [ "3" == "$status" ]; then
          prtMsg "The minimum version of the supported Postgres database is 10, please reconfigure you database server information and try again."
          exit $EXIT_CODE_UNSUPPORTED_POSTGRES_VERSION
        fi
      else
        sudo mkdir -p "$WYN_INSTALL_DIR/wyndbs"
      #else
      #  installDefaultDbProvider
      fi
    fi
  fi
fi

# install MonetDB
if $NEED_INSTALL_SERVER ; then
  needInstallMonetDB
  checkRst=$?
  if [ "0" == "$checkRst" ]; then
    installMonetDB
  elif [ "2" == "$checkRst" ]; then
    uninstallMonetDB
    installMonetDB
  else
    sudo systemctl stop monetdbd > /dev/null 2>&1
  fi
fi

if ! $CONFIG_FILE_EXISTS ; then
  # admin password
  setAdminPassword
  #  default language
  setDefaultLanguage
  # install font
  installFont
fi

# install the sample data
installSampleData

# revise modules installation
if $NEED_INSTALL_SERVER ; then
  sudo mkdir -p "$WYN_INSTALL_DIR/$MODULE_SERVER/Uploads"
  installJRE
  if $NEED_INSTALL_DASHBOARD_WORKER ; then
    sudo echo "/opt/google/chrome/chrome" > "/opt/Wyn/Server/.ChromeExecutablePath"
  fi
fi
if $NEED_INSTALL_REPORTING_WORKER ; then
  if [ -f "$WYN_INSTALL_DIR/$MODULE_REPORTING_WORKER/hosting.json" ]; then
    sudo rm "$WYN_INSTALL_DIR/$MODULE_REPORTING_WORKER/hosting.json"
  fi
fi

if ! $CONFIG_FILE_EXISTS ; then
  updateFirewall
fi

if ! $CONFIG_FILE_EXISTS ; then
  if ! $CLUSTER_MODE ; then
    sudo mv "$SERVICES_FOLDER/$WYN_SERVICE" "$SERVICES_FOLDER/$WYN_SERVICE.bak"
    sudo cp "$SERVICES_FOLDER/$WYN_LIGHT_SERVICE" "$SERVICES_FOLDER/$WYN_SERVICE"
  fi
  createService $WYN_SERVICE $WYN_SERVICE_SEQUENCE
  if ! $CLUSTER_MODE ; then
    sudo rm "$SERVICES_FOLDER/$WYN_SERVICE"
    sudo mv "$SERVICES_FOLDER/$WYN_SERVICE.bak" "$SERVICES_FOLDER/$WYN_SERVICE"
  fi
fi

# update configuration file
if ! $CONFIG_FILE_EXISTS ; then
  updateConfigFile
fi

# start service
startAllServices

# write need installed modules to file
if [ -f "$MODULES_FILE_NAME" ]; then
  sudo mv "$MODULES_FILE_NAME" "$MODULES_FILE"
else
  writeModulesFile
fi

# cleanup useless resources
removeNeedlessModules
cleanup

if ! $CONFIG_FILE_EXISTS && $NEED_INSTALL_SERVER ; then
  waitForWynStartup $SERVER_URI
else
  waitForWynStartup
fi

if [ "0" == "$?" ]; then
  # register license key
  if ! $CONFIG_FILE_EXISTS && $NEED_INSTALL_SERVER && [ "" != "$LICENSE" ] ; then
    registerLicense "http://localhost:$WYN_SERVER_PORT/api/v2/license" "{\"key\":\"$LICENSE\"}"
    if [ "0" != "$?" ]; then
      exit_code=$EXIT_CODE_REGISTER_LICENSE_FAILED
    fi
  fi
else
  exit_code=$EXIT_CODE_START_WYN_FAILED
fi

exit $exit_code
