#!/bin/bash

# input: $1 - input text, $2 - pattern, $3 - replace text
# output: replaced text
strReplace() {
  echo "${1//$2/$3}"
}
# input: $1 - input text
# output: encoded text
XMLEncode() {
  local text="$(strReplace "$1" '&' '&amp;')"
  text="$(strReplace "$text" "'" '&apos;')"
  text="$(strReplace "$text" '"' '&quot;')"
  text="$(strReplace "$text" '<' '&lt;')"
  text="$(strReplace "$text" '>' '&gt;')"
  echo "$text"
}
# get database connection strings
getDbConnectionStrings() {
  if [ "SQLite" == "$DB_PROVIDER" ]; then
    DATABASE_CONNECTIONSTRING_IS="Data source=../wyndbs/is.db;"
    DATABASE_CONNECTIONSTRING_SERVER="Data source=../wyndbs/storage.db;"
    DATABASE_CONNECTIONSTRING_DATACACHE="Data source=../wyndbs/extraction.db;"
    return
  fi
  
  if [ "Postgres" == "$DB_PROVIDER" ]; then
    DB_CONNECTION_STRING="Host=$DB_HOST;Port=$DB_PORT;UserName=$DB_USER;Password=$DB_PASSWORD;"
  elif [ "SqlServer" == "$DB_PROVIDER" ]; then
    DB_CONNECTION_STRING="Data Source=$DB_HOST,$DB_PORT;User ID=$DB_USER;Password=$DB_PASSWORD;"
  elif [ "MySql" == "$DB_PROVIDER" ]; then
    DB_CONNECTION_STRING="Server=$DB_HOST;Port=$DB_PORT;Uid=$DB_USER;Pwd=$DB_PASSWORD;"
  elif [ "Oracle" == "$DB_PROVIDER" ]; then
    DB_CONNECTION_STRING="USER ID=$DB_USER;PASSWORD=$DB_PASSWORD;DATA SOURCE='(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=$DB_HOST)(PORT=$DB_PORT))(CONNECT_DATA=(SERVICE_NAME=$ORACLE_SERVICE_NAME)))'"
  else
    echo "Unknown database provider '$DB_PROVIDER' detected, the supported database providers are 'Postgres', 'SqlServer', 'MySql', 'Oracle' and 'SQLite'."
    exit 1
  fi
  
  DATABASE_WYNIS="$DB_NAME_IDENTITYSERVICE"
  DATABASE_WYNSERVER="$DB_NAME_SERVERDATA"
  DATABASE_WYNDATACACHE="$DB_NAME_DATACACHE"
  if [ "true" == "$SINGLE_DATABASE_MODE" ]; then
    DATABASE_WYNIS="$DB_NAME_WYN"
    DATABASE_WYNSERVER="$DB_NAME_WYN"
    DATABASE_WYNDATACACHE="$DB_NAME_WYN"
  fi
  
  if [ "Postgres" == "$DB_PROVIDER" -o "MySql" == "$DB_PROVIDER" ]; then
    DATABASE_CONNECTIONSTRING_IS="${DB_CONNECTION_STRING}Database=$DATABASE_WYNIS;"
    DATABASE_CONNECTIONSTRING_SERVER="${DB_CONNECTION_STRING}Database=$DATABASE_WYNSERVER;"
    DATABASE_CONNECTIONSTRING_DATACACHE="${DB_CONNECTION_STRING}Database=$DATABASE_WYNDATACACHE;"
  elif [ "SqlServer" == "$DB_PROVIDER" ]; then
    DATABASE_CONNECTIONSTRING_IS="${DB_CONNECTION_STRING}Initial Catalog=$DATABASE_WYNIS;"
    DATABASE_CONNECTIONSTRING_SERVER="${DB_CONNECTION_STRING}Initial Catalog=$DATABASE_WYNSERVER;"
    DATABASE_CONNECTIONSTRING_DATACACHE="${DB_CONNECTION_STRING}Initial Catalog=$DATABASE_WYNDATACACHE;"
  elif [ "Oracle" == "$DB_PROVIDER" ]; then
    DATABASE_CONNECTIONSTRING_IS="${DB_CONNECTION_STRING}"
    DATABASE_CONNECTIONSTRING_SERVER="${DB_CONNECTION_STRING}"
    DATABASE_CONNECTIONSTRING_DATACACHE="${DB_CONNECTION_STRING}"
  fi
  
  DATABASE_CONNECTIONSTRING_IS=$(XMLEncode "$DATABASE_CONNECTIONSTRING_IS")
  DATABASE_CONNECTIONSTRING_SERVER=$(XMLEncode "$DATABASE_CONNECTIONSTRING_SERVER")
  DATABASE_CONNECTIONSTRING_DATACACHE=$(XMLEncode "$DATABASE_CONNECTIONSTRING_DATACACHE")
}
getLanguageCode() {
  if [ "KOREAN" == "${DEFAULT_LANGUAGE^^}" -o "KO-KR" == "${DEFAULT_LANGUAGE^^}" ]; then
    echo "ko-KR"
  else
    echo "en-US"
  fi
}
# setup nginx
setupNginx() {
  if [ "true" == "$REQUIRE_HTTPS" ]; then
    echo "# Default server configuration
server {
  listen 443 ssl http2;

  ssl on;
  ssl_certificate /usr/local/share/ca-certificates/wyn/$SSL_CERTIFICATE_FILE;
  ssl_certificate_key /usr/local/share/ca-certificates/wyn/$SSL_CERTIFICATE_KEY_FILE;
  ssl_protocols TLSv1.2;
  server_name $SITE_NAME;
  large_client_header_buffers 4 32k;

  error_page 504 /custom_504.html;
  location = /custom_504.html {
    root /usr/share/nginx/html;
    internal;
  }

  location / {
    proxy_pass                        http://localhost:51980;
    proxy_http_version                1.1;
    proxy_set_header Upgrade          \$http_upgrade;
    proxy_set_header Connection       keep-alive;
    proxy_set_header Host             \$host;
    proxy_cache_bypass                \$http_upgrade;
    proxy_set_header X-Real-IP        \$remote_addr;
    proxy_set_header X-Forwarded-For  \$proxy_add_x_forwarded_for;
    proxy_connect_timeout             300s;
    proxy_send_timeout                300s;
    proxy_read_timeout                300s;
    send_timeout                      300s;
    sendfile                          on;
    proxy_buffer_size                 64k;
    proxy_buffers                     32 32k;
    proxy_busy_buffers_size           128k;
    fastcgi_buffers                   8 16k;
    fastcgi_buffer_size               32k;
    client_max_body_size              100M;
  }
}" > /etc/nginx/sites-available/default
    nginx -s reload
  fi
}

# write .trialinfo file
if [ ! -f "/wyn/Server/.trialinfo" ]; then
  echo "name: $TRIAL_USER_NAME
company: $TRIAL_USER_COMPANY
email: $TRIAL_USER_EMAIL
phone: $TRIAL_USER_PHONE
country: $TRIAL_USER_COUNTRY
state: $TRIAL_USER_STATE
city: $TRIAL_USER_CITY" > /wyn/Server/.trialinfo
fi

# remove sample files
if [ "true" != "$IMPORT_SAMPLES" ]; then
  rm -rf "/wyn/sampledata/" > /dev/null 2>/dev/null
  rm -rf "/wyn/Server/sample_files/" > /dev/null 2>/dev/null
fi

lngCode=$(getLanguageCode)

# write config file
if [ "false" == "$SINGLE_PROCESS_MODE" ] && [ ! -f "/wyn/Monitor/conf/Wyn.conf" ]; then
  setupNginx
  getDbConnectionStrings
  
  mkdir -p /wyn/wyndbs
  echo "<?xml version=\"1.0\" encoding=\"utf-8\"?>
<SystemConfig xmlns:sys=\"https://extendedxmlserializer.github.io/system\" xmlns=\"clr-namespace:ConfigMigration.Configuration.V90;assembly=ConfigMigration\">
  <Version>9.0</Version>
  <GlobalSettings>
    <IdentityServerUrl>http://localhost:51980</IdentityServerUrl>
    <DataWarehouse>
      <Provider>DuckDB</Provider>
      <ConnectionString>Data source=../wyndbs/analysis.db</ConnectionString>
    </DataWarehouse>
  </GlobalSettings>
  <Services>
    <Server>
      <Urls>http://*:51980</Urls>
      <Storage>
        <StorageType>$DB_PROVIDER</StorageType>
        <ConnectionString>$DATABASE_CONNECTIONSTRING_SERVER</ConnectionString>
      </Storage>
      <DataExtraction>
        <StorageType>$DB_PROVIDER</StorageType>
        <ConnectionString>$DATABASE_CONNECTIONSTRING_DATACACHE</ConnectionString>
      </DataExtraction>
      <IdentityServer>
        <StorageType>$DB_PROVIDER</StorageType>
        <ConnectionString>$DATABASE_CONNECTIONSTRING_IS</ConnectionString>
      </IdentityServer>
      <Culture>
        <Culture>$lngCode</Culture>
      </Culture>" > /wyn/Monitor/conf/Wyn.conf
  if [ "true" == "$REQUIRE_HTTPS" ]; then
    echo "      <RequireHttps>true</RequireHttps>" >> /wyn/Monitor/conf/Wyn.conf
  fi
  if [ "$APPINSIGHTS_KEY" != "" ]; then
    echo "      <ApplicationInsights>
        <UseApplicationInsights>true</UseApplicationInsights>
        <InstrumentationKey>$APPINSIGHTS_KEY</InstrumentationKey>
      </ApplicationInsights>" >> /wyn/Monitor/conf/Wyn.conf
  fi
  echo "    </Server>
    <ReportingWorker />
    <CotWorker />
    <DashboardWorker>
      <BrowserForExport>firefox</BrowserForExport>
      <BrowserPathForExport>/usr/bin/firefox</BrowserPathForExport>
    </DashboardWorker>
    <DataSourceService />
    <MemoryDBService />
    <SchedulerService />
  </Services>
</SystemConfig>" >> /wyn/Monitor/conf/Wyn.conf
fi
if [ "true" == "$SINGLE_PROCESS_MODE" ] && [ ! -f "/wyn/conf/Wyn.conf" ]; then
  setupNginx
  getDbConnectionStrings
  
  mkdir -p /wyn/conf
  mkdir -p /wyn/wyndbs
  echo "<?xml version=\"1.0\" encoding=\"utf-8\"?>
<SystemConfig xmlns:sys=\"https://extendedxmlserializer.github.io/system\" xmlns=\"clr-namespace:ConfigMigration.Configuration.V90;assembly=ConfigMigration\">
  <Version>9.0</Version>
  <GlobalSettings>
    <IdentityServerUrl>http://localhost:51980</IdentityServerUrl>
    <DataWarehouse>
      <Provider>DuckDB</Provider>
      <ConnectionString>Data source=../wyndbs/analysis.db</ConnectionString>
    </DataWarehouse>
  </GlobalSettings>
  <Services>
    <Server>
      <Urls>http://*:51980</Urls>
      <Storage>
        <StorageType>$DB_PROVIDER</StorageType>
        <ConnectionString>$DATABASE_CONNECTIONSTRING_SERVER</ConnectionString>
      </Storage>
      <DataExtraction>
        <StorageType>$DB_PROVIDER</StorageType>
        <ConnectionString>$DATABASE_CONNECTIONSTRING_DATACACHE</ConnectionString>
      </DataExtraction>
      <IdentityServer>
        <StorageType>$DB_PROVIDER</StorageType>
        <ConnectionString>$DATABASE_CONNECTIONSTRING_IS</ConnectionString>
      </IdentityServer>
      <Culture>
        <Culture>$lngCode</Culture>
      </Culture>
      <SchedulerConfig>
        <Mode>Embedded</Mode>
      </SchedulerConfig>
      <ProductArchitecture>FullEmbedded</ProductArchitecture>
      <DisableJavaDataSource>true</DisableJavaDataSource>" > /wyn/conf/Wyn.conf
  if [ "true" == "$REQUIRE_HTTPS" ]; then
    echo "      <RequireHttps>true</RequireHttps>" >> /wyn/conf/Wyn.conf
  fi
  if [ "$APPINSIGHTS_KEY" != "" ]; then
    echo "      <ApplicationInsights>
        <UseApplicationInsights>true</UseApplicationInsights>
        <InstrumentationKey>$APPINSIGHTS_KEY</InstrumentationKey>
      </ApplicationInsights>" >> /wyn/conf/Wyn.conf
  fi
  echo "    </Server>
    <ReportingWorker />
    <CotWorker />
    <DashboardWorker>
      <BrowserForExport>firefox</BrowserForExport>
      <BrowserPathForExport>/usr/bin/firefox</BrowserPathForExport>
    </DashboardWorker>
  </Services>
</SystemConfig>" >> /wyn/conf/Wyn.conf
fi

# start or stop nginx
if [ "true" == "$REQUIRE_HTTPS" ]; then
  service nginx start > /dev/null 2>&1
else
  service nginx stop > /dev/null 2>&1
fi

sleep 5

# startup wyn monitor
if [ "true" == "$SINGLE_PROCESS_MODE" ]; then
  cd /wyn/Server
  exec dotnet Gces.Server.dll --RunAsMonitor
else
  cd /wyn/Monitor
  exec dotnet ServiceMonitor.dll
fi
