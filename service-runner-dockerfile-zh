FROM guanjinke/aspnetcore-3.1-postgres-monetdb
ADD ./wyn_zh ./app

RUN echo 'Acquire::https::dev.monetdb.org::Verify-Peer "false";' > /etc/apt/apt.conf.d/99monetdb-cert && \
    apt-get update && \
    apt-get install -y wget && \
    apt-get install -y gnupg apt-transport-https ca-certificates && \
    wget --no-check-certificate --output-document=- https://www.monetdb.org/downloads/MonetDB-GPG-KEY.gpg | apt-key add - && \
    wget --output-document=- https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -

RUN echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list && \
    apt-get update && \
    apt-get install -y google-chrome-stable

WORKDIR /app/ServiceRunner
EXPOSE 5000
ENTRYPOINT dotnet /app/ServiceRunner/ServiceRunner.dll