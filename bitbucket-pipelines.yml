image: guanjinke/gces-build-3.1:1.0

pipelines:
  branches:
    develop:
      - step:
          name: download assets and unzip files
          script: 
            - mkdir downloads
            - dotnet ./Tools/Downloader3.1/gces-artifacts-downloader.dll ./develop/manifest.txt ./downloads true ./develop/version-detect.txt
            # unzip to en
            - mkdir wyn_en
            - unzip ./downloads/service-runner-en.*.zip -d ./wyn_en/ServiceRunner
            - unzip ./downloads/server-en.*.zip -d ./wyn_en/Server
            - unzip ./downloads/analysis-model.*.zip -d ./wyn_en/Plugins
            - unzip ./downloads/dashboard.*.zip -d ./wyn_en/Plugins
            - unzip ./downloads/dataset-and-account.*.zip -d ./wyn_en/Plugins
            - unzip ./downloads/reporting.*.zip -d ./wyn_en/Plugins
            - unzip ./downloads/gcef-datasource-service.*.zip -d ./wyn_en/DataSourceService
            - unzip ./downloads/gces-memorydb-service.*.zip -d ./wyn_en/MemoryDBService
            - unzip ./downloads/scheduler.service.*.zip -d ./wyn_en/SchedulerService
            - unzip ./downloads/Gces.MonetDb.Cluster.Bootstrap.Host.*.zip -d ./wyn_en/AnalysisDBPrimaryNode
            - unzip ./downloads/Gces.MonetDb.Cluster.Server.*.zip -d ./wyn_en/AnalysisDBService
            # correct wyn folder content
            - mv ./wyn_en/Plugins/plugin ./wyn_en/Plugins/Reporting
            - mv ./wyn_en/Plugins/cot-worker ./wyn_en/CotWorker
            - mv ./wyn_en/Plugins/dashboard ./wyn_en/Plugins/Dashboard
            - mv ./wyn_en/Plugins/dashboard-worker ./wyn_en/DashboardWorker
            - mv ./wyn_en/Plugins/dataset ./wyn_en/Plugins/Dataset
            - mv ./wyn_en/Plugins/account ./wyn_en/Plugins/Account
            - mv ./wyn_en/Plugins/worker ./wyn_en/ReportingWorker
            - mv ./wyn_en/Plugins/analysis ./wyn_en/Plugins/AnalysisModel
            - rm -rf ./wyn_en/Plugins/Web Contents

            # unzip to zh
            - mkdir wyn_zh
            - unzip ./downloads/service-runner-en.*.zip -d ./wyn_zh/ServiceRunner
            - unzip ./downloads/server-zh.*.zip -d ./wyn_zh/Server
            - unzip ./downloads/analysis-model.*.zip -d ./wyn_zh/Plugins
            - unzip ./downloads/dashboard.*.zip -d ./wyn_zh/Plugins
            - unzip ./downloads/dataset-and-account.*.zip -d ./wyn_zh/Plugins
            - unzip ./downloads/reporting.*.zip -d ./wyn_zh/Plugins
            - unzip ./downloads/gcef-datasource-service.*.zip -d ./wyn_zh/DataSourceService
            - unzip ./downloads/gces-memorydb-service.*.zip -d ./wyn_zh/MemoryDBService
            - unzip ./downloads/scheduler.service.*.zip -d ./wyn_zh/SchedulerService
            - unzip ./downloads/Gces.MonetDb.Cluster.Bootstrap.Host.*.zip -d ./wyn_zh/AnalysisDBPrimaryNode
            - unzip ./downloads/Gces.MonetDb.Cluster.Server.*.zip -d ./wyn_zh/AnalysisDBService
            # correct wyn folder content
            - mv ./wyn_zh/Plugins/plugin ./wyn_zh/Plugins/Reporting
            - mv ./wyn_zh/Plugins/cot-worker ./wyn_zh/CotWorker
            - mv ./wyn_zh/Plugins/dashboard ./wyn_zh/Plugins/Dashboard
            - mv ./wyn_zh/Plugins/dashboard-worker ./wyn_zh/DashboardWorker
            - mv ./wyn_zh/Plugins/dataset ./wyn_zh/Plugins/Dataset
            - mv ./wyn_zh/Plugins/account ./wyn_zh/Plugins/Account
            - mv ./wyn_zh/Plugins/worker ./wyn_zh/ReportingWorker
            - mv ./wyn_zh/Plugins/analysis ./wyn_zh/Plugins/AnalysisModel
            - mv "./wyn_zh/Plugins/Web Contents" "./wyn_zh/Server/wwwroot/Web Contents"
          artifacts:
            - wyn_en/**
            - wyn_zh/**
      - step:
          name: Publish to Docker
          script:
            - VERSION=0.1.$BITBUCKET_BUILD_NUMBER
            - docker login gcefdockerregistry.azurecr.io -u ${DOCKER_REGISTRY_USER} -p ${DOCKER_REGISTRY_PWD}

            # images with dotnet runtime
            - IMAGE_NAME_ZH=gces-service-runner-zh
            - IMAGE_NAME_EN=gces-service-runner-en
            #
            - docker build -t ${IMAGE_NAME_ZH} -f service-runner-dockerfile-zh .
            - docker build -t ${IMAGE_NAME_EN} -f service-runner-dockerfile-en .
            # tag local image with version and latest tags
            - docker tag ${IMAGE_NAME_ZH} gcefdockerregistry.azurecr.io/${IMAGE_NAME_ZH}:${VERSION}
            - docker tag ${IMAGE_NAME_ZH} gcefdockerregistry.azurecr.io/${IMAGE_NAME_ZH}:latest
            - docker tag ${IMAGE_NAME_EN} gcefdockerregistry.azurecr.io/${IMAGE_NAME_EN}:${VERSION}
            - docker tag ${IMAGE_NAME_EN} gcefdockerregistry.azurecr.io/${IMAGE_NAME_EN}:latest
            # push local images to private repository
            - docker push gcefdockerregistry.azurecr.io/${IMAGE_NAME_ZH}:${VERSION}
            - docker push gcefdockerregistry.azurecr.io/${IMAGE_NAME_ZH}:latest
            - docker push gcefdockerregistry.azurecr.io/${IMAGE_NAME_EN}:${VERSION}
            - docker push gcefdockerregistry.azurecr.io/${IMAGE_NAME_EN}:latest

            # update build status with description
            - BUILD_STATUS="{\"key\":\"docker_image\", \"state\":\"SUCCESSFUL\", \"name\":\"Update docker image.\", \"url\":\"http://gcefdockerregistry.azurecr.io/${IMAGE_NAME_ZH}\", \"description\":\"Updated docker image in gcefdockerregistry.azurecr.io. Get newest version by docker pull gcefdockerregistry.azurecr.io/${IMAGE_NAME_ZH}:${VERSION}\"}"
            - curl -H "Content-Type:application/json" -X POST --user "${BB_AUTH_STRING}" -d "${BUILD_STATUS}" "https://api.bitbucket.org/2.0/repositories/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/commit/${BITBUCKET_COMMIT}/statuses/build"
          services:
            - docker