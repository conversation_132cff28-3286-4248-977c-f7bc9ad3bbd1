#!/bin/bash

echo "pre-removal checking..."

systemctl --version > /dev/null 2>/dev/null
if [ "0" == "$?" ]; then
  sudo systemctl is-active -q wyn > /dev/null 2>/dev/null
  if [ "0" == "$?" ]; then
    echo "stopping wyn.service..."
    sudo systemctl stop wyn > /dev/null 2>/dev/null
    sudo systemctl stop wyn-datasource > /dev/null 2>/dev/null
  fi
else
  sudo service wyn status -q > /dev/null 2>/dev/null
  if [ "0" == "$?" ]; then
    echo "stopping service wyn..."
    sudo service wyn stop > /dev/null 2>/dev/null
    sudo service wyn-datasource stop > /dev/null 2>/dev/null
  fi
fi

exit 0
