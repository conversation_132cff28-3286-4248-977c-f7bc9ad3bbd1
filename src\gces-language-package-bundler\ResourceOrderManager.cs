using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Unicode;

namespace gces_language_package_bundler
{
	public class ResourceOrderInfo
	{
		public string Module { get; set; }
		public string DisplayName { get; set; }
		public int Order { get; set; }
		public bool Obsolete { get; set; }
		public string Path { get; set; }
	}

	public class ResourceOrderManager
	{
		private readonly string _resourceConfigEntryName = "language-resources.json";
		private readonly string _orderConfigFileName = "resource-order.json";
		private readonly string _configDirPath;
		private readonly string[] _repoNameOrder = new[]
		{
			"gces-server",
			"gcef-identity-service",
			"gces-common",
			"gces-analysis",
			"gces-dashboard",
			"gces-reporting",
			"gces-forguncy",
			"gces-js-common",
		};

		public ResourceOrderManager(string configDirPath)
		{
			_configDirPath = configDirPath;
			if (!Directory.Exists(_configDirPath))
			{
				Directory.CreateDirectory(_configDirPath);
			}
		}

		private Dictionary<string, int> GetRepoOrder()
		{
			var order = new Dictionary<string, int>();
			for (int i = 0; i < _repoNameOrder.Length; i++)
			{
				order[_repoNameOrder[i]] = i * 10000;
			}
			return order;
		}

		private string GetOrderConfigPath(string lng)
		{
			return Path.Combine(_configDirPath, $"{lng}-{_orderConfigFileName}");
		}

		private List<ResourceOrderInfo> GetExistingResourceOrder(string lng)
		{
			var configPath = GetOrderConfigPath(lng);
			try
			{
				if (File.Exists(configPath))
				{
					var content = File.ReadAllText(configPath);
					return JsonSerializer.Deserialize<List<ResourceOrderInfo>>(content);
				}
			}
			catch (Exception) { }
			return new List<ResourceOrderInfo>();
		}

		private void SaveOrderConfig(string lng, List<ResourceOrderInfo> resources)
		{
			var configPath = GetOrderConfigPath(lng);
			var json = JsonSerializer.Serialize(
				resources,
				new JsonSerializerOptions 
				{ 
					WriteIndented = true,
					Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
				}
			);

			var isNewFile = !File.Exists(configPath);
			File.WriteAllText(configPath, json, new UTF8Encoding(false));
			Console.WriteLine($"{(isNewFile ? "Created" : "Updated")} order config file: {configPath}");
		}

		private string UnifyPathExt(string path)
		{
			return Path.ChangeExtension(path, "json");
		}

		public void SaveResourceOrder(
			string zipPath,
			string lng,
			List<string> lngArtifacts,
			List<(string RepoName, string OutputFile)> targetFiles
		)
		{
			var repoOrderDict = GetRepoOrder();
			var currentLngResources = new List<ResourceOrderInfo>();
			const int defaultOrder = 1000000;

			var existingResources = GetExistingResourceOrder(lng);

			var maxExistingOrder = existingResources.Any()
				? existingResources.Max(r => r.Order)
				: 0;

			var existingPaths = new HashSet<string>(
				existingResources.Select(r => UnifyPathExt(r.Path))
			);
			var currentPaths = new HashSet<string>();

			foreach (var lngArtifact in lngArtifacts)
			{
				var repoName = targetFiles
					.FirstOrDefault(f => f.OutputFile == lngArtifact)
					.RepoName;
				using var fileStream = File.OpenRead(lngArtifact);
				using var artifactArchive = new ZipArchive(fileStream);

				var resourceConfig = artifactArchive.Entries.FirstOrDefault(
					e => e.Name == _resourceConfigEntryName
				);
				if (resourceConfig != null)
				{
					using var resourceConfigStream = resourceConfig.Open();
					var resourceConfigContent = new StreamReader(resourceConfigStream).ReadToEnd();
					var resourceConfigObj = JsonSerializer.Deserialize<ResourceConfig>(
						resourceConfigContent,
						new JsonSerializerOptions() { PropertyNameCaseInsensitive = true }
					);

					var currentResource = resourceConfigObj.Resources.FirstOrDefault(
						r => r.Lng == lng
					);
					if (currentResource != null)
					{
						var uniqueFiles = currentResource.Files
							.GroupBy(f => f.Name)
							.Select(g => g.OrderBy(f => f.Order).First())
							.ToList();

						foreach (var file in uniqueFiles)
						{
							var path = file.Name.Replace("./", $"{repoName}/");
							var pathWithUnifiedExt = UnifyPathExt(path);
							currentPaths.Add(pathWithUnifiedExt);

							var matchingResource = existingResources.FirstOrDefault(
								r => UnifyPathExt(r.Path) == pathWithUnifiedExt
							);
							if (matchingResource != null)
							{
								matchingResource.Path = path;
							}
							else
							{
								var resourceFile = new ResourceOrderInfo
								{
									Module = file.Module,
									DisplayName = file.DisplayName,
									Order =
										maxExistingOrder
										+ (
											repoOrderDict.TryGetValue(repoName, out var order)
												? order
												: defaultOrder
										)
										+ file.Order.Value,
									Obsolete = file.Obsolete,
									Path = path
								};
								currentLngResources.Add(resourceFile);
							}
						}
					}
				}
			}

			var missingFiles = existingPaths.Except(currentPaths).ToList();
			if (missingFiles.Any())
			{
				Console.WriteLine($"Warning: The following files exist in the old order config file, but were not found in the current resource configs:");
				foreach (var missingFile in missingFiles)
				{
					Console.WriteLine($"  - {missingFile}");
				}
			}

			var allResources = existingResources.Concat(currentLngResources).ToList();
			var orderedResources = allResources
				.OrderBy(r => r.Order)
				.Select(
					(r, index) =>
					{
						r.Order = index + 1;
						return r;
					}
				)
				.ToList();

			if (orderedResources.Any())
			{
				SaveOrderConfig(lng, orderedResources);

				using var packageStream = new FileStream(zipPath, FileMode.Open);
				using var lngPackFile = new ZipArchive(packageStream, ZipArchiveMode.Update);

				var orderConfigEntry = lngPackFile.CreateEntry(
					_orderConfigFileName,
					CompressionLevel.NoCompression
				);
				using var orderConfigStream = orderConfigEntry.Open();
				using var writer = new StreamWriter(orderConfigStream, new UTF8Encoding(false));
				var orderConfigJson = JsonSerializer.Serialize(
					orderedResources,
					new JsonSerializerOptions 
					{ 
						WriteIndented = true,
						Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
					}
				);
				writer.Write(orderConfigJson);
			}
		}
	}
}
