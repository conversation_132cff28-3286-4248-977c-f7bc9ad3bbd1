﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace gces_language_package_bundler
{
	public class ExclusiveConfig
	{
		public string Edition { get; set; }
		public List<string> ExclusivePlugins { get; set; }

		public ExclusiveConfig()
		{

		}
	}

	public class PackageManifest
	{
		public string Edition { get; set; }
		public string LanguageCode { get; set; }
		public string Name { get; set; }
		public string Author { get; set; } = "Builtin";
		public DateTime PublishTime { get; set; } = DateTime.UtcNow;
		public DateTime CreatedTime { get; set; } = DateTime.UtcNow;
		public string CreatedBy { get; set; } = "Builtin";
		public DateTime ModifiedTime { get; set; } = DateTime.UtcNow;
		public string ModifiedBy { get; set; } = "Builtin";
		public string Version { get; set; } = "0.0.1";
		public string WynVersion { get; set; }
		public string ReferenceLanguage { get; set; }

		public PackageManifest(string edition, string name, string languageCode, string wynVersion)
		{
			Edition = edition;
			Name = name;
			LanguageCode = languageCode;
			WynVersion = wynVersion;
			ReferenceLanguage = languageCode;
		}
	}

	public class LngResourceConfigValidateRes
	{
		public bool Valid { get; set; } = true;
		public Dictionary<string, Dictionary<string, List<ResourceFile>>> InvalidDisplayNameItems { get; set; } = new Dictionary<string, Dictionary<string, List<ResourceFile>>>();
		public Dictionary<string, IGrouping<string, ResourceFile>> ModuleAndNameDuplicatedItems { get; set; } = new Dictionary<string, IGrouping<string, ResourceFile>>();
		public Dictionary<string, List<ResourceFile>> InvalidOrderValueItems { get; set; } = new Dictionary<string, List<ResourceFile>>();
		public Dictionary<string, List<ResourceFile>> MultiOrdersItems { get; set; } = new Dictionary<string, List<ResourceFile>>();
		public Dictionary<string, List<ResourceFile>> MultiObsoleteItems { get; set; } = new Dictionary<string, List<ResourceFile>>();
		public Dictionary<string, List<IGrouping<int?, ResourceFile>>> OrderDuplicatedItems { get; set; } = new Dictionary<string, List<IGrouping<int?, ResourceFile>>>();
	}

	public class ResourceFile
	{
		public string Name { get; set; }
		public string NameSpace { get; set; }
		public string TargetKeys { get; set; }
		public string Module { get; set; }
		public string DisplayName { get; set; }
		public int? Order { get; set; }
		public bool Obsolete { get; set; }
		public ResourceFile()
		{

		}
	}

	public class ResourceItem
	{
		public string Lng { get; set; }
		public List<ResourceFile> Files { get; set; }
	}

	public class ResourceConfig
	{
		public List<ResourceItem> Resources { get; set; }
	}

	public class RepoResourceValidation
	{
		public string RepoName { get; set; }
		public List<ResourceFile> Files { get; set; }
	}
}
